import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import DashboardPage from '../app/dashboard/page'
import { mockContracts, mockUsers, renderWithProviders } from '@clm/testing'

// Mock tRPC
const mockTrpcClient = {
  contracts: {
    getAll: {
      useQuery: vi.fn(),
    },
  },
  users: {
    getStatistics: {
      useQuery: vi.fn(),
    },
  },
}

vi.mock('@/lib/trpc', () => ({
  trpc: mockTrpcClient,
}))

describe('DashboardPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render welcome message', () => {
    // Mock successful data loading
    mockTrpcClient.contracts.getAll.useQuery.mockReturnValue({
      data: { contracts: [mockContracts.active] },
      isLoading: false,
      error: null,
    })

    mockTrpcClient.users.getStatistics.useQuery.mockReturnValue({
      data: { total: 10, active: 8 },
      isLoading: false,
      error: null,
    })

    render(
      renderWithProviders(<DashboardPage />, { user: mockUsers.admin }).container
    )

    expect(screen.getByText('Selamat Datang di CLM Platform')).toBeInTheDocument()
    expect(screen.getByText('Kelola kontrak Anda dengan mudah dan efisien')).toBeInTheDocument()
  })

  it('should display contract statistics', async () => {
    const mockContractsData = {
      contracts: [mockContracts.active, mockContracts.draft],
    }

    mockTrpcClient.contracts.getAll.useQuery.mockReturnValue({
      data: mockContractsData,
      isLoading: false,
      error: null,
    })

    mockTrpcClient.users.getStatistics.useQuery.mockReturnValue({
      data: { total: 10, active: 8 },
      isLoading: false,
      error: null,
    })

    render(
      renderWithProviders(<DashboardPage />, { user: mockUsers.admin }).container
    )

    await waitFor(() => {
      expect(screen.getByText('Total Kontrak')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument() // Number of contracts
    })
  })

  it('should show loading state for contracts', () => {
    mockTrpcClient.contracts.getAll.useQuery.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    })

    mockTrpcClient.users.getStatistics.useQuery.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    })

    render(
      renderWithProviders(<DashboardPage />, { user: mockUsers.admin }).container
    )

    expect(screen.getByText('...')).toBeInTheDocument()
  })

  it('should display quick action buttons', () => {
    mockTrpcClient.contracts.getAll.useQuery.mockReturnValue({
      data: { contracts: [] },
      isLoading: false,
      error: null,
    })

    mockTrpcClient.users.getStatistics.useQuery.mockReturnValue({
      data: { total: 0, active: 0 },
      isLoading: false,
      error: null,
    })

    render(
      renderWithProviders(<DashboardPage />, { user: mockUsers.admin }).container
    )

    expect(screen.getByText('Buat Kontrak')).toBeInTheDocument()
    expect(screen.getByText('Gunakan Template')).toBeInTheDocument()
    expect(screen.getByText('Cari Kontrak')).toBeInTheDocument()
  })

  it('should handle error state gracefully', () => {
    mockTrpcClient.contracts.getAll.useQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error('Failed to fetch contracts'),
    })

    mockTrpcClient.users.getStatistics.useQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error('Failed to fetch user stats'),
    })

    render(
      renderWithProviders(<DashboardPage />, { user: mockUsers.admin }).container
    )

    // Should still render the page structure
    expect(screen.getByText('Selamat Datang di CLM Platform')).toBeInTheDocument()
    expect(screen.getByText('Aksi Cepat')).toBeInTheDocument()
  })

  it('should display recent contracts section', async () => {
    const mockContractsData = {
      contracts: [mockContracts.active, mockContracts.draft],
    }

    mockTrpcClient.contracts.getAll.useQuery.mockReturnValue({
      data: mockContractsData,
      isLoading: false,
      error: null,
    })

    mockTrpcClient.users.getStatistics.useQuery.mockReturnValue({
      data: { total: 10, active: 8 },
      isLoading: false,
      error: null,
    })

    render(
      renderWithProviders(<DashboardPage />, { user: mockUsers.admin }).container
    )

    await waitFor(() => {
      expect(screen.getByText('Kontrak Terbaru')).toBeInTheDocument()
    })
  })
})
