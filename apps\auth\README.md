# CLM Auth Service

Dedicated authentication service using BetterAuth for the Contract Lifecycle Management platform.

## Features

- Email/password authentication
- Google OAuth integration
- JWT session management
- Prisma database adapter
- Cross-origin support for multiple apps

## Setup

1. Copy `.env.example` to `.env` and configure:
   ```bash
   cp .env.example .env
   ```

2. Update environment variables:
   - `DATABASE_URL`: PostgreSQL connection string
   - `BETTER_AUTH_SECRET`: Random secret for JWT signing
   - `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET`: For OAuth (optional)

3. Run database migrations:
   ```bash
   npx prisma migrate dev
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

The auth service will run on `http://localhost:3003`

## API Endpoints

- `GET/POST /api/auth/*` - All authentication endpoints
- Main endpoints include:
  - `/api/auth/sign-in` - Sign in
  - `/api/auth/sign-up` - Register
  - `/api/auth/sign-out` - Sign out
  - `/api/auth/session` - Get current session

## Integration

Other apps in the monorepo can use the shared `@clm/auth` package for client-side authentication.