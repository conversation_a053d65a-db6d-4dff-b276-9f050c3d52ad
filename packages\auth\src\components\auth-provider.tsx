"use client"

import React, { createContext, useContext, useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useSession, signOut } from "../client"
import type { User, Session } from "../config"

interface AuthContextType {
  user: User | null
  session: Session | null
  isLoading: boolean
  isAuthenticated: boolean
  signOut: () => Promise<void>
  refreshSession: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: React.ReactNode
  requireAuth?: boolean
  requiredRole?: string
  requiredRoles?: string[]
  fallback?: React.ReactNode
}

export function AuthProvider({
  children,
  requireAuth = false,
  requiredRole,
  requiredRoles,
  fallback,
}: AuthProviderProps) {
  const [session] = useAtom(sessionAtom);
  const router = useRouter()
  const pathname = usePathname()
  const [isInitialized, setIsInitialized] = useState(false)

  const user = session.data?.user || null
  const sessionData = session.data?.session || null
  const isLoading = session.isPending || !isInitialized
  const isAuthenticated = !!user

  // Initialize auth state
  useEffect(() => {
    if (!session.isPending) {
      setIsInitialized(true)
    }
  }, [session.isPending])

  // Handle authentication requirements
  useEffect(() => {
    if (!isLoading && requireAuth && !isAuthenticated) {
      const loginUrl = `/login?redirect=${encodeURIComponent(pathname)}`
      router.push(loginUrl)
      return
    }

    if (!isLoading && isAuthenticated && user) {
      // Check if user is active
      if (!user.isActive) {
        handleSignOut()
        return
      }

      // Check role requirements
      if (requiredRole && user.role !== requiredRole) {
        router.push("/unauthorized")
        return
      }

      if (requiredRoles && !requiredRoles.includes(user.role || "")) {
        router.push("/unauthorized")
        return
      }
    }
  }, [
    isLoading,
    isAuthenticated,
    user,
    requireAuth,
    requiredRole,
    requiredRoles,
    router,
    pathname,
  ])

  const handleSignOut = async () => {
    try {
      await signOut()
      router.push("/login")
    } catch (error) {
      console.error("Sign out error:", error)
      // Force redirect even if sign out fails
      router.push("/login")
    }
  }

  const refreshSession = () => {
    // Trigger session refresh
    session.refetch()
  }

  const value: AuthContextType = {
    user,
    session: sessionData,
    isLoading,
    isAuthenticated,
    signOut: handleSignOut,
    refreshSession,
  }

  // Show loading state
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      )
    )
  }

  // Show unauthorized state
  if (requireAuth && !isAuthenticated) {
    return null // Will redirect to login
  }

  if (
    isAuthenticated &&
    user &&
    ((requiredRole && user.role !== requiredRole) ||
      (requiredRoles && !requiredRoles.includes(user.role || "")))
  ) {
    return null // Will redirect to unauthorized
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

// Higher-order component for protected routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requireAuth?: boolean
    requiredRole?: string
    requiredRoles?: string[]
    fallback?: React.ReactNode
  }
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <AuthProvider {...options}>
        <Component {...props} />
      </AuthProvider>
    )
  }
}

// Hook for permission checking
export function usePermissions() {
  const { user } = useAuth()

  const hasRole = (role: string) => {
    return user?.role === role
  }

  const hasAnyRole = (roles: string[]) => {
    return roles.includes(user?.role || "")
  }

  const canAccessAdmin = () => {
    return hasAnyRole(["ADMIN", "LEGAL"])
  }

  const canManageUsers = () => {
    return hasRole("ADMIN")
  }

  const canManageContracts = () => {
    return hasAnyRole(["ADMIN", "LEGAL", "FINANCE"])
  }

  const canViewContracts = () => {
    return hasAnyRole(["ADMIN", "LEGAL", "FINANCE", "USER", "VIEWER"])
  }

  const canCreateContracts = () => {
    return hasAnyRole(["ADMIN", "LEGAL", "USER"])
  }

  const canApproveContracts = () => {
    return hasAnyRole(["ADMIN", "LEGAL"])
  }

  const canEditContract = (contractCreatorId: string) => {
    if (hasAnyRole(["ADMIN", "LEGAL"])) return true
    return user?.id === contractCreatorId
  }

  const canDeleteContract = (contractCreatorId: string) => {
    if (hasRole("ADMIN")) return true
    return user?.id === contractCreatorId
  }

  return {
    hasRole,
    hasAnyRole,
    canAccessAdmin,
    canManageUsers,
    canManageContracts,
    canViewContracts,
    canCreateContracts,
    canApproveContracts,
    canEditContract,
    canDeleteContract,
  }
}

// Component for conditional rendering based on permissions
interface PermissionGateProps {
  children: React.ReactNode
  role?: string
  roles?: string[]
  permission?: (permissions: ReturnType<typeof usePermissions>) => boolean
  fallback?: React.ReactNode
}

export function PermissionGate({
  children,
  role,
  roles,
  permission,
  fallback = null,
}: PermissionGateProps) {
  const permissions = usePermissions()

  let hasPermission = true

  if (role) {
    hasPermission = permissions.hasRole(role)
  } else if (roles) {
    hasPermission = permissions.hasAnyRole(roles)
  } else if (permission) {
    hasPermission = permission(permissions)
  }

  return hasPermission ? <>{children}</> : <>{fallback}</>
}
