{"name": "@clm/admin", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --port 3001", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "clean": "rm -rf .next"}, "dependencies": {"@clm/api": "*", "@clm/auth": "*", "@clm/ui": "*", "@clm/lib": "*", "@trpc/client": "^10.45.2", "@trpc/next": "^10.45.2", "@trpc/server": "^10.45.2", "better-auth": "^0.7.1", "next": "^14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "superjson": "^2.2.1"}, "devDependencies": {"@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "postcss": "^8.4.39", "tailwindcss": "^3.4.4", "typescript": "^5.5.3"}}