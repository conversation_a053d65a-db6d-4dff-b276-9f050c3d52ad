{"name": "@clm/lib", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/mime-types": "^2.1.4", "@types/node": "^20.14.10", "@types/node-forge": "^1.3.11", "eslint": "^8.57.0", "typescript": "^5.5.3"}, "dependencies": {"@clm/db": "*", "bcryptjs": "^2.4.3", "date-fns": "^3.6.0", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "nanoid": "^5.0.7", "zod": "^3.23.8", "node-forge": "^1.3.1", "resend": "^3.4.0", "bottleneck": "^2.19.5", "@google/generative-ai": "^0.14.1", "@opensearch-project/opensearch": "^2.6.0"}}