import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { Header } from '../components/layout/header'
import { mockUsers, renderWithProviders } from '@clm/testing'

// Mock auth hook
vi.mock('@clm/auth', () => ({
  useAuth: () => ({
    user: mockUsers.admin,
  }),
}))

describe('Header', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render dashboard title', () => {
    render(renderWithProviders(<Header />, { user: mockUsers.admin }).container)
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
  })

  it('should display search input', () => {
    render(renderWithProviders(<Header />, { user: mockUsers.admin }).container)
    
    const searchInput = screen.getByPlaceholderText('<PERSON><PERSON> kontrak...')
    expect(searchInput).toBeInTheDocument()
    expect(searchInput).toHaveAttribute('type', 'text')
  })

  it('should render notification button', () => {
    render(renderWithProviders(<Header />, { user: mockUsers.admin }).container)
    
    const notificationButton = screen.getByText('🔔')
    expect(notificationButton).toBeInTheDocument()
  })

  it('should display user information', () => {
    render(renderWithProviders(<Header />, { user: mockUsers.admin }).container)
    
    expect(screen.getByText('Admin CLM')).toBeInTheDocument()
    expect(screen.getByText('A')).toBeInTheDocument() // Avatar initial
  })

  it('should handle search input changes', () => {
    render(renderWithProviders(<Header />, { user: mockUsers.admin }).container)
    
    const searchInput = screen.getByPlaceholderText('Cari kontrak...')
    fireEvent.change(searchInput, { target: { value: 'test contract' } })
    
    expect(searchInput).toHaveValue('test contract')
  })

  it('should render user avatar with correct styling', () => {
    render(renderWithProviders(<Header />, { user: mockUsers.admin }).container)
    
    const avatar = screen.getByText('A')
    expect(avatar).toHaveClass('w-8', 'h-8', 'bg-blue-500', 'rounded-full')
  })

  it('should display correct user name', () => {
    const customUser = { ...mockUsers.admin, name: 'John Doe' }
    
    render(renderWithProviders(<Header />, { user: customUser }).container)
    
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('J')).toBeInTheDocument() // Avatar initial
  })

  it('should handle user with no name gracefully', () => {
    const userWithoutName = { ...mockUsers.admin, name: '' }
    
    render(renderWithProviders(<Header />, { user: userWithoutName }).container)
    
    // Should not crash
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
  })

  it('should have proper styling classes', () => {
    render(renderWithProviders(<Header />, { user: mockUsers.admin }).container)
    
    const header = screen.getByRole('banner')
    expect(header).toHaveClass('bg-white', 'border-b', 'border-gray-200', 'h-16')
  })

  it('should render search input with correct styling', () => {
    render(renderWithProviders(<Header />, { user: mockUsers.admin }).container)
    
    const searchInput = screen.getByPlaceholderText('Cari kontrak...')
    expect(searchInput).toHaveClass('w-64', 'px-3', 'py-2', 'border', 'border-gray-300', 'rounded-md')
  })

  it('should handle notification button click', () => {
    render(renderWithProviders(<Header />, { user: mockUsers.admin }).container)
    
    const notificationButton = screen.getByText('🔔')
    fireEvent.click(notificationButton)
    
    // Should not crash when clicked
    expect(notificationButton).toBeInTheDocument()
  })

  it('should display user menu section', () => {
    render(renderWithProviders(<Header />, { user: mockUsers.admin }).container)
    
    const userSection = screen.getByText('Admin CLM').closest('div')
    expect(userSection).toHaveClass('flex', 'items-center', 'space-x-2')
  })
})
