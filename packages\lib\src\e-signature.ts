export interface ESignatureProvider {
  sendForSignature(document: <PERSON><PERSON><PERSON>, recipient: { name: string; email: string }): Promise<{ success: boolean; documentId: string }>;
  getSignatureStatus(documentId: string): Promise<'pending' | 'signed' | 'declined'>;
}

class MockESignatureProvider implements ESignatureProvider {
  async sendForSignature(document: <PERSON><PERSON><PERSON>, recipient: { name: string; email: string }) {
    console.log(`Sending document to ${recipient.name} (${recipient.email}) for signature.`);
    return { success: true, documentId: `mock-${Date.now()}` };
  }

  async getSignatureStatus(documentId: string) {
    return 'signed' as const;
  }
}

export const eSignatureProvider = new MockESignatureProvider();
