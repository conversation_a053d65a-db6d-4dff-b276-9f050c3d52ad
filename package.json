{"name": "clm-platform", "version": "1.0.0", "description": "Contract Lifecycle Management Platform for Indonesia", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "type-check": "turbo run type-check", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build --filter=!@clm/docs && changeset publish"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@turbo/gen": "^1.12.4", "eslint": "^8.57.0", "prettier": "^3.2.5", "turbo": "^1.12.4", "typescript": "^5.3.3"}, "packageManager": "npm@10.2.4", "engines": {"node": ">=18"}}