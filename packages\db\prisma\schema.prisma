// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management (Better Auth compatible)
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  emailVerified Boolean   @default(false)
  name          String
  avatar        String?
  role          UserRole  @default(USER)
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Better Auth fields
  sessions      Session[]
  accounts      Account[]

  // Relations
  contracts       Contract[]
  comments        Comment[]
  workflowSteps   WorkflowStep[]
  auditLogs       AuditLog[]
  notifications   Notification[]
  createdTemplates Template[] @relation("TemplateCreator")

  @@map("user")
}

// Better Auth Session model
model Session {
  id        String   @id @default(cuid())
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

// Better Auth Account model (for OAuth providers)
model Account {
  id                String  @id @default(cuid())
  accountId         String
  providerId        String
  userId            String
  accessToken       String?
  refreshToken      String?
  idToken           String?
  accessTokenExpiresAt DateTime?
  refreshTokenExpiresAt DateTime?
  scope             String?
  password          String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([providerId, accountId])
  @@map("account")
}

// Better Auth Verification model
model Verification {
  id         String   @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([identifier, value])
  @@map("verification")
}

enum UserRole {
  ADMIN
  LEGAL
  FINANCE
  USER
  VIEWER
}

// Contract Management
model Contract {
  id          String        @id @default(cuid())
  title       String
  description String?
  content     String?
  fileUrl     String?
  fileName    String?
  fileSize    Int?
  mimeType    String?
  status      ContractStatus @default(DRAFT)
  priority    Priority      @default(MEDIUM)
  value       Decimal?
  currency    String        @default("IDR")
  startDate   DateTime?
  endDate     DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  createdBy   String
  creator     User     @relation(fields: [createdBy], references: [id])
  
  // Contract parties
  parties     ContractParty[]
  
  // Workflow and approval
  workflow    Workflow?
  workflowSteps WorkflowStep[]
  
  // Comments and collaboration
  comments    Comment[]
  
  // Versions and history
  versions    ContractVersion[]
  
  // AI Analysis
  aiAnalysis  AIAnalysis?
  
  // Audit trail
  auditLogs   AuditLog[]
  
  // Notifications
  notifications Notification[]

  @@map("contracts")
}

enum ContractStatus {
  DRAFT
  REVIEW
  APPROVED
  SIGNED
  ACTIVE
  EXPIRED
  TERMINATED
  REJECTED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// Contract Parties
model ContractParty {
  id         String @id @default(cuid())
  name       String
  email      String?
  phone      String?
  address    String?
  role       PartyRole
  contractId String
  contract   Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())

  @@map("contract_parties")
}

enum PartyRole {
  CLIENT
  VENDOR
  PARTNER
  INTERNAL
}

// Contract Versions
model ContractVersion {
  id         String   @id @default(cuid())
  version    Int
  content    String?
  fileUrl    String?
  changes    String?
  contractId String
  contract   Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())

  @@unique([contractId, version])
  @@map("contract_versions")
}

// Workflow Management
model Workflow {
  id          String @id @default(cuid())
  name        String
  description String?
  isActive    Boolean @default(true)
  contractId  String? @unique
  contract    Contract? @relation(fields: [contractId], references: [id])
  
  // Workflow steps
  steps       WorkflowStep[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("workflows")
}

model WorkflowStep {
  id          String           @id @default(cuid())
  name        String
  description String?
  order       Int
  status      WorkflowStatus   @default(PENDING)
  assignedTo  String?
  assignee    User?            @relation(fields: [assignedTo], references: [id])
  completedAt DateTime?
  comments    String?
  
  workflowId  String?
  workflow    Workflow?        @relation(fields: [workflowId], references: [id])
  
  contractId  String?
  contract    Contract?        @relation(fields: [contractId], references: [id])
  
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  @@map("workflow_steps")
}

enum WorkflowStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  REJECTED
  SKIPPED
}

// Comments and Collaboration
model Comment {
  id         String   @id @default(cuid())
  content    String
  contractId String
  contract   Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)
  authorId   String
  author     User     @relation(fields: [authorId], references: [id])
  parentId   String?
  parent     Comment? @relation("CommentReplies", fields: [parentId], references: [id])
  replies    Comment[] @relation("CommentReplies")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("comments")
}

// AI Analysis
model AIAnalysis {
  id          String   @id @default(cuid())
  summary     String?
  keyPoints   Json?
  riskFactors Json?
  extractedData Json?
  confidence  Float?
  contractId  String   @unique
  contract    Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("ai_analysis")
}

// Templates
model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  content     String
  category    String?
  isActive    Boolean  @default(true)
  createdBy   String
  creator     User     @relation("TemplateCreator", fields: [createdBy], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("templates")
}

// Notifications
model Notification {
  id         String           @id @default(cuid())
  title      String
  message    String
  type       NotificationType
  isRead     Boolean          @default(false)
  userId     String
  user       User             @relation(fields: [userId], references: [id])
  contractId String?
  contract   Contract?        @relation(fields: [contractId], references: [id])
  createdAt  DateTime         @default(now())

  @@map("notifications")
}

enum NotificationType {
  CONTRACT_CREATED
  CONTRACT_UPDATED
  WORKFLOW_ASSIGNED
  WORKFLOW_COMPLETED
  COMMENT_ADDED
  MENTION
  DEADLINE_REMINDER
  SYSTEM_ALERT
}

// Audit Trail
model AuditLog {
  id         String     @id @default(cuid())
  action     String
  entity     String
  entityId   String
  oldValues  Json?
  newValues  Json?
  userId     String?
  user       User?      @relation(fields: [userId], references: [id])
  contractId String?
  contract   Contract?  @relation(fields: [contractId], references: [id])
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime   @default(now())

  @@map("audit_logs")
}
