import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import { UserRole } from '@clm/db'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || 60 * 60 * 24 * 7 // 7 days

export interface JWTPayload {
  userId: string
  email: string
  role: UserRole
  iat?: number
  exp?: number
}

export interface AuthUser {
  id: string
  email: string
  name: string
  role: UserRole
  avatar?: string
}

// JWT utilities
export function signToken(payload: Omit<JWTPayload, 'iat' | 'exp'>) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: Number(JWT_EXPIRES_IN) })
}

export function verifyToken(token: string): JWTPayload {
  return jwt.verify(token, JWT_SECRET) as JWTPayload
}

export function decodeToken(token: string): JWTPayload | null {
  try {
    return jwt.decode(token) as JWTPayload
  } catch {
    return null
  }
}

export function isTokenExpired(token: string): boolean {
  const decoded = decodeToken(token)
  if (!decoded || !decoded.exp) return true
  return Date.now() >= decoded.exp * 1000
}

// Password utilities
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

export async function comparePassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

// Role-based access control
export function hasPermission(userRole: UserRole, requiredRole: UserRole): boolean {
  const roleHierarchy: Record<UserRole, number> = {
    VIEWER: 1,
    USER: 2,
    FINANCE: 3,
    LEGAL: 4,
    ADMIN: 5,
  }
  
  return roleHierarchy[userRole] >= roleHierarchy[requiredRole]
}

export function canAccessContract(userRole: UserRole, contractCreatorId: string, userId: string): boolean {
  // Admin and Legal can access all contracts
  if (userRole === 'ADMIN' || userRole === 'LEGAL') return true
  
  // Users can access their own contracts
  if (contractCreatorId === userId) return true
  
  // Finance can access contracts for financial review
  if (userRole === 'FINANCE') return true
  
  return false
}

export function canModifyContract(userRole: UserRole, contractCreatorId: string, userId: string): boolean {
  // Admin can modify all contracts
  if (userRole === 'ADMIN') return true
  
  // Users can modify their own contracts if they're in draft
  if (contractCreatorId === userId) return true
  
  // Legal can modify contracts for legal review
  if (userRole === 'LEGAL') return true
  
  return false
}

export function canDeleteContract(userRole: UserRole, contractCreatorId: string, userId: string): boolean {
  // Only admin and contract creator can delete
  return userRole === 'ADMIN' || contractCreatorId === userId
}

// Session utilities
export function createSession(user: AuthUser): { user: AuthUser; expires: string } {
  const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
  return {
    user,
    expires,
  }
}

export function isSessionValid(expires: string): boolean {
  return new Date(expires) > new Date()
}

// Security headers
export const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
} as const

// Rate limiting
export interface RateLimitConfig {
  windowMs: number
  maxRequests: number
}

export const rateLimits: Record<string, RateLimitConfig> = {
  login: { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 attempts per 15 minutes
  register: { windowMs: 60 * 60 * 1000, maxRequests: 3 }, // 3 attempts per hour
  api: { windowMs: 15 * 60 * 1000, maxRequests: 100 }, // 100 requests per 15 minutes
  upload: { windowMs: 60 * 60 * 1000, maxRequests: 10 }, // 10 uploads per hour
}
