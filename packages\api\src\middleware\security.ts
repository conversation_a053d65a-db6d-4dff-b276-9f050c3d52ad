import { TRPCError } from '@trpc/server'
import { verifyToken, type JWTPayload } from '@clm/lib'
import { prisma } from '@clm/db'

export async function createContext(req: Request): Promise<{
  session: { user: any } | null
  req: Request
}> {
  const authHeader = req.headers.get('authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { session: null, req }
  }

  const token = authHeader.substring(7)

  try {
    const payload = verifyToken(token) as JWTPayload
    
    // Verify user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        avatar: true,
        isActive: true,
      },
    })

    if (!user || !user.isActive) {
      return { session: null, req }
    }

    return {
      session: {
        user,
      },
      req,
    }
  } catch (error) {
    return { session: null, req }
  }
}

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export function rateLimit(
  identifier: string,
  maxRequests: number,
  windowMs: number
): boolean {
  const now = Date.now()
  const key = identifier
  const record = rateLimitStore.get(key)

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (record.count >= maxRequests) {
    return false
  }

  record.count++
  return true
}

export function checkRateLimit(
  identifier: string,
  maxRequests: number,
  windowMs: number
) {
  if (!rateLimit(identifier, maxRequests, windowMs)) {
    throw new TRPCError({
      code: 'TOO_MANY_REQUESTS',
      message: 'Rate limit exceeded. Please try again later.',
    })
  }
}

// Input sanitization
export function sanitizeInput(input: string): string {
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .trim()
}

export function validateFileUpload(file: {
  name: string
  size: number
  type: string
}): void {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
  ]

  const maxSize = 10 * 1024 * 1024 // 10MB

  if (!allowedTypes.includes(file.type)) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'File type not allowed. Only PDF, DOC, DOCX, and TXT files are permitted.',
    })
  }

  if (file.size > maxSize) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'File size too large. Maximum size is 10MB.',
    })
  }

  // Check for potentially dangerous file names
  const dangerousPatterns = [
    /\.\./,
    /[<>:"|?*]/,
    /^(con|prn|aux|nul|com[1-9]|lpt[1-9])$/i,
  ]

  if (dangerousPatterns.some(pattern => pattern.test(file.name))) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Invalid file name.',
    })
  }
}

// CSRF protection
export function generateCSRFToken(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15)
}

export function validateCSRFToken(token: string, sessionToken: string): boolean {
  return token === sessionToken
}

// Security headers middleware
export function addSecurityHeaders(response: Response): Response {
  const headers = new Headers(response.headers)
  
  headers.set('X-Content-Type-Options', 'nosniff')
  headers.set('X-Frame-Options', 'DENY')
  headers.set('X-XSS-Protection', '1; mode=block')
  headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
  headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers,
  })
}

// Audit logging
export async function logSecurityEvent(
  event: string,
  userId?: string,
  details?: Record<string, any>,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await prisma.auditLog.create({
    data: {
      action: event,
      entity: 'Security',
      entityId: 'system',
      newValues: details,
      userId,
      ipAddress,
      userAgent,
    },
  })
}
