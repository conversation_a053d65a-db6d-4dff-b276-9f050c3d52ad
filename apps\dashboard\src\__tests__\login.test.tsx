import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, mockUsers } from '@clm/testing'
import LoginPage from '../app/login/page'

// Mock Next.js navigation
const mockPush = vi.fn()
const mockReplace = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
}))

// Mock SignInForm component
vi.mock('@clm/auth', () => ({
  SignInForm: ({ redirectTo, showDemoAccounts, onSuccess }: any) => (
    <div data-testid="sign-in-form">
      <h2>Sign In Form</h2>
      <p>Redirect to: {redirectTo}</p>
      <p>Show demo accounts: {showDemoAccounts ? 'Yes' : 'No'}</p>
      <button onClick={() => onSuccess && onSuccess()}>Sign In</button>
    </div>
  ),
}))

describe('Login Page', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render login page with correct title and description', () => {
    render(<LoginPage />)

    expect(screen.getByText('CLM Platform')).toBeInTheDocument()
    expect(screen.getByText('Contract Lifecycle Management Indonesia')).toBeInTheDocument()
  })

  it('should render SignInForm with correct props', () => {
    render(<LoginPage />)

    const signInForm = screen.getByTestId('sign-in-form')
    expect(signInForm).toBeInTheDocument()
    expect(screen.getByText('Redirect to: /dashboard')).toBeInTheDocument()
    expect(screen.getByText('Show demo accounts: Yes')).toBeInTheDocument()
  })

  it('should have proper page structure and styling', () => {
    const { container } = render(<LoginPage />)

    // Check for main container with proper classes
    const mainContainer = container.querySelector('.min-h-screen')
    expect(mainContainer).toBeInTheDocument()
    expect(mainContainer).toHaveClass('flex', 'items-center', 'justify-center', 'bg-gray-50')

    // Check for content wrapper
    const contentWrapper = container.querySelector('.max-w-md')
    expect(contentWrapper).toBeInTheDocument()
    expect(contentWrapper).toHaveClass('w-full', 'space-y-8')
  })

  it('should display platform branding correctly', () => {
    render(<LoginPage />)

    const title = screen.getByRole('heading', { level: 1 })
    expect(title).toHaveTextContent('CLM Platform')
    expect(title).toHaveClass('text-3xl', 'font-bold', 'text-gray-900')

    const subtitle = screen.getByText('Contract Lifecycle Management Indonesia')
    expect(subtitle).toHaveClass('mt-2', 'text-gray-600')
  })

  it('should be accessible', () => {
    render(<LoginPage />)

    // Check for proper heading hierarchy
    const mainHeading = screen.getByRole('heading', { level: 1 })
    expect(mainHeading).toBeInTheDocument()

    // Check for proper semantic structure
    expect(screen.getByTestId('sign-in-form')).toBeInTheDocument()
  })

  it('should handle responsive design', () => {
    const { container } = render(<LoginPage />)

    // Check for responsive padding classes
    const mainContainer = container.querySelector('.min-h-screen')
    expect(mainContainer).toHaveClass('py-12', 'px-4')

    // Check for responsive spacing
    const contentWrapper = container.querySelector('.max-w-md')
    expect(contentWrapper).toHaveClass('space-y-8')
  })
})
