import { useRouter } from 'next/router'
import { Button } from '@clm/ui'

export default function UnauthorizedPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        <div className="text-6xl mb-4">🚫</div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-6">
          You don't have permission to access the admin panel. This area is restricted to administrators only.
        </p>
        <div className="space-y-4">
          <Button 
            onClick={() => router.push('/login')}
            className="w-full"
          >
            Back to Login
          </Button>
          <Button 
            variant="outline"
            onClick={() => window.location.href = 'http://localhost:3000'}
            className="w-full"
          >
            Go to Main Dashboard
          </Button>
        </div>
      </div>
    </div>
  )
}
