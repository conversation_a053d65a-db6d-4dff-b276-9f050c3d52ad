import { format, formatDistanceToNow, isValid, parseISO } from 'date-fns'
import { id } from 'date-fns/locale'

export function formatDate(date: Date | string, pattern = 'dd/MM/yyyy'): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  if (!isValid(dateObj)) return 'Invalid Date'
  return format(dateObj, pattern, { locale: id })
}

export function formatDateTime(date: Date | string): string {
  return formatDate(date, 'dd/MM/yyyy HH:mm')
}

export function formatRelativeTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  if (!isValid(dateObj)) return 'Invalid Date'
  return formatDistanceToNow(dateObj, { addSuffix: true, locale: id })
}

export function formatCurrency(
  amount: number,
  currency = 'IDR',
  locale = 'id-ID'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount)
}

export function formatNumber(
  number: number,
  locale = 'id-ID'
): string {
  return new Intl.NumberFormat(locale).format(number)
}

