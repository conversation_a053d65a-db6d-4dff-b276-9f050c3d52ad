import { Server, Socket } from 'socket.io'
import { z } from 'zod'
import { prisma } from '@clm/db'

interface NotificationData {
  id: string
  title: string
  message: string
  type: string
  userId: string
  contractId?: string
  isRead: boolean
  createdAt: Date
}

const markAsReadSchema = z.object({
  notificationId: z.string(),
})

const markAllAsReadSchema = z.object({
  userId: z.string().optional(),
})

export class NotificationHandler {
  constructor(private io: Server) {}

  handleConnection(socket: Socket) {
    const user = socket.data.user

    // Join user's personal notification room
    const userRoom = `user:${user.id}`
    socket.join(userRoom)

    // Send unread notifications count on connection
    this.sendUnreadCount(socket, user.id)

    // Handle mark notification as read
    socket.on('notification:mark-read', async (data) => {
      try {
        const { notificationId } = markAsReadSchema.parse(data)
        await this.markAsRead(socket, notificationId)
      } catch (error) {
        socket.emit('error', { message: 'Invalid notification data' })
      }
    })

    // <PERSON>le mark all notifications as read
    socket.on('notification:mark-all-read', async (data) => {
      try {
        const { userId } = markAllAsReadSchema.parse(data)
        await this.markAllAsRead(socket, userId || user.id)
      } catch (error) {
        socket.emit('error', { message: 'Invalid notification data' })
      }
    })

    // Handle get notifications
    socket.on('notification:get-list', async (data) => {
      try {
        const { limit = 20, offset = 0 } = data || {}
        await this.getNotifications(socket, user.id, limit, offset)
      } catch (error) {
        socket.emit('error', { message: 'Failed to get notifications' })
      }
    })
  }

  // Send notification to specific user
  async sendToUser(userId: string, notification: Omit<NotificationData, 'id' | 'userId' | 'createdAt'>) {
    try {
      // Save to database
      const savedNotification = await prisma.notification.create({
        data: {
          title: notification.title,
          message: notification.message,
          type: notification.type as any,
          userId,
          contractId: notification.contractId,
          isRead: false,
        },
      })

      // Send via WebSocket
      this.io.to(`user:${userId}`).emit('notification:new', {
        id: savedNotification.id,
        title: savedNotification.title,
        message: savedNotification.message,
        type: savedNotification.type,
        contractId: savedNotification.contractId,
        isRead: savedNotification.isRead,
        createdAt: savedNotification.createdAt,
      })

      // Update unread count
      this.sendUnreadCountToUser(userId)

      return savedNotification
    } catch (error) {
      console.error('Failed to send notification:', error)
      throw error
    }
  }

  // Send notification to multiple users
  async sendToUsers(userIds: string[], notification: Omit<NotificationData, 'id' | 'userId' | 'createdAt'>) {
    const promises = userIds.map(userId => this.sendToUser(userId, notification))
    return Promise.all(promises)
  }

  // Send notification to all users with specific role
  async sendToRole(role: string, notification: Omit<NotificationData, 'id' | 'userId' | 'createdAt'>) {
    try {
      const users = await prisma.user.findMany({
        where: { role: role as any, isActive: true },
        select: { id: true },
      })

      const userIds = users.map(user => user.id)
      return this.sendToUsers(userIds, notification)
    } catch (error) {
      console.error('Failed to send notification to role:', error)
      throw error
    }
  }

  // Contract-specific notifications
  async sendContractNotification(
    contractId: string,
    notification: Omit<NotificationData, 'id' | 'userId' | 'createdAt' | 'contractId'>,
    excludeUserId?: string
  ) {
    try {
      // Get contract participants
      const contract = await prisma.contract.findUnique({
        where: { id: contractId },
        include: {
          creator: true,
          comments: {
            include: { author: true },
            distinct: ['authorId'],
          },
          workflowSteps: {
            include: { assignee: true },
            where: { assignedTo: { not: null } },
          },
        },
      })

      if (!contract) {
        throw new Error('Contract not found')
      }

      // Collect all relevant user IDs
      const userIds = new Set<string>()
      
      // Add contract creator
      userIds.add(contract.createdBy)
      
      // Add comment authors
      contract.comments.forEach(comment => {
        if (comment.author) {
          userIds.add(comment.author.id)
        }
      })
      
      // Add workflow assignees
      contract.workflowSteps.forEach(step => {
        if (step.assignee) {
          userIds.add(step.assignee.id)
        }
      })

      // Remove excluded user
      if (excludeUserId) {
        userIds.delete(excludeUserId)
      }

      // Send notifications
      const notificationWithContract = {
        ...notification,
        contractId,
      }

      return this.sendToUsers(Array.from(userIds), notificationWithContract)
    } catch (error) {
      console.error('Failed to send contract notification:', error)
      throw error
    }
  }

  private async markAsRead(socket: Socket, notificationId: string) {
    try {
      const user = socket.data.user

      await prisma.notification.updateMany({
        where: {
          id: notificationId,
          userId: user.id,
        },
        data: {
          isRead: true,
        },
      })

      socket.emit('notification:marked-read', { notificationId })
      this.sendUnreadCount(socket, user.id)
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
      socket.emit('error', { message: 'Failed to mark notification as read' })
    }
  }

  private async markAllAsRead(socket: Socket, userId: string) {
    try {
      await prisma.notification.updateMany({
        where: {
          userId,
          isRead: false,
        },
        data: {
          isRead: true,
        },
      })

      socket.emit('notification:all-marked-read')
      this.sendUnreadCount(socket, userId)
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
      socket.emit('error', { message: 'Failed to mark all notifications as read' })
    }
  }

  private async getNotifications(socket: Socket, userId: string, limit: number, offset: number) {
    try {
      const notifications = await prisma.notification.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        include: {
          contract: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      })

      socket.emit('notification:list', {
        notifications: notifications.map(notification => ({
          id: notification.id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          contractId: notification.contractId,
          contract: notification.contract,
          isRead: notification.isRead,
          createdAt: notification.createdAt,
        })),
        hasMore: notifications.length === limit,
      })
    } catch (error) {
      console.error('Failed to get notifications:', error)
      socket.emit('error', { message: 'Failed to get notifications' })
    }
  }

  private async sendUnreadCount(socket: Socket, userId: string) {
    try {
      const count = await prisma.notification.count({
        where: {
          userId,
          isRead: false,
        },
      })

      socket.emit('notification:unread-count', { count })
    } catch (error) {
      console.error('Failed to get unread count:', error)
    }
  }

  private async sendUnreadCountToUser(userId: string) {
    try {
      const count = await prisma.notification.count({
        where: {
          userId,
          isRead: false,
        },
      })

      this.io.to(`user:${userId}`).emit('notification:unread-count', { count })
    } catch (error) {
      console.error('Failed to send unread count to user:', error)
    }
  }

  // Predefined notification types
  static NotificationTypes = {
    CONTRACT_CREATED: 'CONTRACT_CREATED',
    CONTRACT_UPDATED: 'CONTRACT_UPDATED',
    CONTRACT_APPROVED: 'CONTRACT_APPROVED',
    CONTRACT_REJECTED: 'CONTRACT_REJECTED',
    CONTRACT_SIGNED: 'CONTRACT_SIGNED',
    WORKFLOW_ASSIGNED: 'WORKFLOW_ASSIGNED',
    WORKFLOW_COMPLETED: 'WORKFLOW_COMPLETED',
    COMMENT_ADDED: 'COMMENT_ADDED',
    COMMENT_MENTION: 'COMMENT_MENTION',
    DEADLINE_REMINDER: 'DEADLINE_REMINDER',
    SYSTEM_ALERT: 'SYSTEM_ALERT',
  } as const

  // Helper methods for common notifications
  async notifyContractCreated(contractId: string, contractTitle: string, creatorId: string) {
    return this.sendContractNotification(contractId, {
      title: 'Kontrak Baru Dibuat',
      message: `Kontrak "${contractTitle}" telah dibuat`,
      type: NotificationHandler.NotificationTypes.CONTRACT_CREATED,
      isRead: false,
    }, creatorId)
  }

  async notifyContractUpdated(contractId: string, contractTitle: string, updaterId: string) {
    return this.sendContractNotification(contractId, {
      title: 'Kontrak Diperbarui',
      message: `Kontrak "${contractTitle}" telah diperbarui`,
      type: NotificationHandler.NotificationTypes.CONTRACT_UPDATED,
      isRead: false,
    }, updaterId)
  }

  async notifyWorkflowAssigned(userId: string, contractId: string, contractTitle: string, stepName: string) {
    return this.sendToUser(userId, {
      title: 'Tugas Workflow Baru',
      message: `Anda ditugaskan untuk "${stepName}" pada kontrak "${contractTitle}"`,
      type: NotificationHandler.NotificationTypes.WORKFLOW_ASSIGNED,
      contractId,
      isRead: false,
    })
  }

  async notifyCommentAdded(contractId: string, contractTitle: string, commenterName: string, commenterId: string) {
    return this.sendContractNotification(contractId, {
      title: 'Komentar Baru',
      message: `${commenterName} menambahkan komentar pada kontrak "${contractTitle}"`,
      type: NotificationHandler.NotificationTypes.COMMENT_ADDED,
      isRead: false,
    }, commenterId)
  }
}
