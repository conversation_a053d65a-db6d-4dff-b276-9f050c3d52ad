import { NextRequest, NextResponse } from "next/server"
import { auth } from "./config"

export async function authMiddleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  
  // Public routes that don't require authentication
  const publicRoutes = [
    "/",
    "/login",
    "/register",
    "/forgot-password",
    "/reset-password",
    "/verify-email",
    "/api/auth",
  ]
  
  // Admin routes that require admin role
  const adminRoutes = [
    "/admin",
    "/dashboard/users",
    "/dashboard/settings",
    "/dashboard/audit",
  ]
  
  // Check if route is public
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(`${route}/`)
  )
  
  if (isPublicRoute) {
    return NextResponse.next()
  }
  
  try {
    // Get session from request
    const session = await auth.api.getSession({
      headers: request.headers,
    })
    
    if (!session?.user) {
      // Redirect to login if not authenticated
      const loginUrl = new URL("/login", request.url)
      loginUrl.searchParams.set("redirect", pathname)
      return NextResponse.redirect(loginUrl)
    }
    
    // Check if user is active
    if (!session.user.isActive) {
      const response = NextResponse.json(
        { error: "Account is deactivated" },
        { status: 403 }
      )
      return response
    }
    
    // Check admin routes
    const isAdminRoute = adminRoutes.some(route => 
      pathname === route || pathname.startsWith(`${route}/`)
    )
    
    if (isAdminRoute && !["ADMIN", "LEGAL"].includes(session.user.role || "")) {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      )
    }
    
    // Add user info to headers for downstream use
    const response = NextResponse.next()
    response.headers.set("x-user-id", session.user.id)
    response.headers.set("x-user-role", session.user.role || "USER")
    response.headers.set("x-user-email", session.user.email)
    
    return response
    
  } catch (error) {
    console.error("Auth middleware error:", error)
    
    // Redirect to login on auth errors
    const loginUrl = new URL("/login", request.url)
    loginUrl.searchParams.set("redirect", pathname)
    return NextResponse.redirect(loginUrl)
  }
}

// Rate limiting middleware
export async function rateLimitMiddleware(request: NextRequest) {
  const ip = request.ip || request.headers.get("x-forwarded-for") || "unknown"
  const pathname = request.nextUrl.pathname
  
  // Different rate limits for different endpoints
  const rateLimits = {
    "/api/auth/sign-in": { window: 15 * 60 * 1000, max: 5 }, // 5 attempts per 15 minutes
    "/api/auth/sign-up": { window: 60 * 60 * 1000, max: 3 }, // 3 attempts per hour
    "/api/auth/reset-password": { window: 60 * 60 * 1000, max: 3 }, // 3 attempts per hour
    "/api": { window: 15 * 60 * 1000, max: 100 }, // 100 requests per 15 minutes
  }
  
  // Find matching rate limit
  let rateLimit = rateLimits["/api"] // default
  for (const [path, limit] of Object.entries(rateLimits)) {
    if (pathname.startsWith(path)) {
      rateLimit = limit
      break
    }
  }
  
  // In production, use Redis for rate limiting
  // For now, use in-memory storage (not suitable for production)
  const key = `rate_limit:${ip}:${pathname}`
  
  // This is a simplified rate limiting - in production use Redis
  // with sliding window or token bucket algorithm
  
  return NextResponse.next()
}

// Security headers middleware
export function securityHeadersMiddleware(request: NextRequest) {
  const response = NextResponse.next()
  
  // Security headers
  response.headers.set("X-Content-Type-Options", "nosniff")
  response.headers.set("X-Frame-Options", "DENY")
  response.headers.set("X-XSS-Protection", "1; mode=block")
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin")
  response.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()")
  
  if (process.env.NODE_ENV === "production") {
    response.headers.set(
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains; preload"
    )
    response.headers.set(
      "Content-Security-Policy",
      "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';"
    )
  }
  
  return response
}

// Combined middleware
export const middleware = auth.handler; {
  // Apply security headers first
  let response = securityHeadersMiddleware(request)
  
  // Apply rate limiting
  response = await rateLimitMiddleware(request)
  
  // Apply authentication
  response = await authMiddleware(request)
  
  return response
}

// Middleware configuration
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
}
