import { prisma } from '../index'
import { Prisma } from '@prisma/client'
import bcrypt from 'bcryptjs'

export class AuthRepository {
  async createUser(data: {
    email: string
    name: string
    password: string
    role?: string
  }) {
    const hashedPassword = await bcrypt.hash(data.password, 10)
    
    return prisma.user.create({
      data: {
        email: data.email,
        name: data.name,
        role: data.role as any || 'USER',
        emailVerified: false,
        accounts: {
          create: {
            accountId: data.email,
            providerId: 'credential',
            password: hashedPassword,
          },
        },
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        emailVerified: true,
        createdAt: true,
      },
    })
  }

  async findUserByEmail(email: string) {
    return prisma.user.findUnique({
      where: { email },
      include: {
        accounts: {
          where: { providerId: 'credential' },
          select: {
            password: true,
          },
        },
      },
    })
  }

  async findUserById(id: string) {
    return prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        avatar: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
      },
    })
  }

  async updateUser(id: string, data: Partial<{
    name: string
    email: string
    role: string
    isActive: boolean
    avatar: string
    emailVerified: boolean
  }>) {
    return prisma.user.update({
      where: { id },
      data: {
        ...data,
        role: data.role as any,
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        avatar: true,
        emailVerified: true,
        updatedAt: true,
      },
    })
  }

  async updatePassword(userId: string, newPassword: string) {
    const hashedPassword = await bcrypt.hash(newPassword, 10)
    
    // Update password in account table
    await prisma.account.updateMany({
      where: {
        userId,
        providerId: 'credential',
      },
      data: {
        password: hashedPassword,
      },
    })

    return prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        updatedAt: true,
      },
    })
  }

  async verifyPassword(plainPassword: string, hashedPassword: string) {
    return bcrypt.compare(plainPassword, hashedPassword)
  }

  async deactivateUser(id: string) {
    return prisma.user.update({
      where: { id },
      data: { isActive: false },
      select: {
        id: true,
        email: true,
        isActive: true,
        updatedAt: true,
      },
    })
  }

  async activateUser(id: string) {
    return prisma.user.update({
      where: { id },
      data: { isActive: true },
      select: {
        id: true,
        email: true,
        isActive: true,
        updatedAt: true,
      },
    })
  }

  async deleteUser(id: string) {
    // Check if user is admin (prevent deleting admin users)
    const user = await prisma.user.findUnique({
      where: { id },
      select: { role: true },
    })

    if (user?.role === 'ADMIN') {
      throw new Error('Cannot delete admin users')
    }

    return prisma.user.delete({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
      },
    })
  }

  async getAllUsers(options: {
    limit?: number
    offset?: number
    search?: string
    role?: string
    isActive?: boolean
  } = {}) {
    const { limit = 10, offset = 0, search, role, isActive } = options

    const where: Prisma.UserWhereInput = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (role) {
      where.role = role as any
    }

    if (typeof isActive === 'boolean') {
      where.isActive = isActive
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          avatar: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              contracts: true,
              comments: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      }),
      prisma.user.count({ where }),
    ])

    return {
      users,
      total,
      hasMore: offset + limit < total,
    }
  }

  async getUserStats() {
    const [total, active, inactive, byRole, recentUsers] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.user.count({ where: { isActive: false } }),
      prisma.user.groupBy({
        by: ['role'],
        _count: { role: true },
      }),
      prisma.user.findMany({
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
      }),
    ])

    const roleStats = byRole.reduce((acc, item) => {
      acc[item.role] = item._count.role
      return acc
    }, {} as Record<string, number>)

    return {
      total,
      active,
      inactive,
      byRole: roleStats,
      recentUsers,
    }
  }

  async createSession(userId: string, token: string, expiresAt: Date, ipAddress?: string, userAgent?: string) {
    return prisma.session.create({
      data: {
        userId,
        token,
        expiresAt,
        ipAddress,
        userAgent,
      },
      select: {
        id: true,
        token: true,
        expiresAt: true,
        createdAt: true,
      },
    })
  }

  async findSessionByToken(token: string) {
    return prisma.session.findUnique({
      where: { token },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            isActive: true,
            avatar: true,
            emailVerified: true,
          },
        },
      },
    })
  }

  async deleteSession(token: string) {
    return prisma.session.delete({
      where: { token },
      select: {
        id: true,
        token: true,
      },
    })
  }

  async deleteUserSessions(userId: string) {
    return prisma.session.deleteMany({
      where: { userId },
    })
  }

  async cleanupExpiredSessions() {
    return prisma.session.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    })
  }

  async getUserSessions(userId: string) {
    return prisma.session.findMany({
      where: { userId },
      select: {
        id: true,
        token: true,
        expiresAt: true,
        ipAddress: true,
        userAgent: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: 'desc' },
    })
  }

  // Admin-only methods
  async promoteToAdmin(userId: string, promotedBy: string) {
    // Log the promotion for audit
    await prisma.auditLog.create({
      data: {
        action: 'USER_PROMOTED_TO_ADMIN',
        entity: 'USER',
        entityId: userId,
        userId: promotedBy,
        newValues: {
          promotedUserId: userId,
          newRole: 'ADMIN',
        },
      },
    })

    return prisma.user.update({
      where: { id: userId },
      data: { role: 'ADMIN' },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        updatedAt: true,
      },
    })
  }

  async demoteFromAdmin(userId: string, demotedBy: string, newRole: string = 'USER') {
    // Prevent demoting the last admin
    const adminCount = await prisma.user.count({
      where: { role: 'ADMIN', isActive: true },
    })

    if (adminCount <= 1) {
      throw new Error('Cannot demote the last admin user')
    }

    // Log the demotion for audit
    await prisma.auditLog.create({
      data: {
        action: 'USER_DEMOTED_FROM_ADMIN',
        entity: 'USER',
        entityId: userId,
        userId: demotedBy,
        oldValues: {
          oldRole: 'ADMIN',
        },
        newValues: {
          demotedUserId: userId,
          newRole,
        },
      },
    })

    return prisma.user.update({
      where: { id: userId },
      data: { role: newRole as any },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        updatedAt: true,
      },
    })
  }

  async getAdminUsers() {
    return prisma.user.findMany({
      where: { role: 'ADMIN', isActive: true },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: 'asc' },
    })
  }
}
