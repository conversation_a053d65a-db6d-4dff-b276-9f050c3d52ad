import { pki } from 'node-forge';

export function verifyCertificateChain(certificateChain: string[], trustedCa: string) {
  try {
    const caStore = pki.createCaStore([trustedCa]);
    const certificates = certificateChain.map((cert) => pki.certificateFromPem(cert));
    return pki.verifyCertificateChain(caStore, certificates);
  } catch (error) {
    console.error('Error verifying certificate chain:', error);
    return false;
  }
}
