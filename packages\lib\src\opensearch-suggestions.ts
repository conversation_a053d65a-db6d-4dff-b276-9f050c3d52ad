import { opensearchClient } from './opensearch';

export async function getSearchSuggestions(query: string) {
  const response = await opensearchClient.search({
    index: 'contracts',
    body: {
      suggest: {
        text: query,
        simple_phrase: {
          phrase: {
            field: 'attachment.content',
            size: 1,
            gram_size: 3,
            direct_generator: [
              {
                field: 'attachment.content',
                suggest_mode: 'always',
              },
            ],
          },
        },
      },
    },
  });

  return response.body.suggest.simple_phrase;
}
