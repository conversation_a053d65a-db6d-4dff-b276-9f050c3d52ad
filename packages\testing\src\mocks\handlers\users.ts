import { http, HttpResponse } from 'msw'

const mockUsers = [
  {
    id: 'test-admin-id',
    email: '<EMAIL>',
    name: 'Ad<PERSON> CLM',
    role: 'ADMIN',
    isActive: true,
    avatar: null,
    emailVerified: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'test-legal-id',
    email: '<EMAIL>',
    name: 'Legal Team',
    role: 'LEGAL',
    isActive: true,
    avatar: null,
    emailVerified: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Regular User',
    role: 'USER',
    isActive: true,
    avatar: null,
    emailVerified: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'test-inactive-user-id',
    email: '<EMAIL>',
    name: 'Inactive User',
    role: 'USER',
    isActive: false,
    avatar: null,
    emailVerified: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-15T00:00:00.000Z',
  },
]

export const userHandlers = [
  // Get user statistics
  http.get('/api/trpc/users.getStatistics', () => {
    const stats = {
      total: mockUsers.length,
      active: mockUsers.filter(u => u.isActive).length,
      inactive: mockUsers.filter(u => !u.isActive).length,
      byRole: {
        ADMIN: mockUsers.filter(u => u.role === 'ADMIN').length,
        LEGAL: mockUsers.filter(u => u.role === 'LEGAL').length,
        FINANCE: mockUsers.filter(u => u.role === 'FINANCE').length,
        USER: mockUsers.filter(u => u.role === 'USER').length,
        VIEWER: mockUsers.filter(u => u.role === 'VIEWER').length,
      },
      recentRegistrations: mockUsers.slice(-5),
    }

    return HttpResponse.json({
      result: {
        data: stats,
      },
    })
  }),

  // Get all users
  http.get('/api/trpc/users.getAll', ({ request }) => {
    const url = new URL(request.url)
    const input = JSON.parse(url.searchParams.get('input') || '{}')
    const { limit = 10, offset = 0, search, role, isActive } = input

    let filteredUsers = mockUsers

    if (search) {
      filteredUsers = mockUsers.filter(user =>
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
      )
    }

    if (role) {
      filteredUsers = filteredUsers.filter(user => user.role === role)
    }

    if (typeof isActive === 'boolean') {
      filteredUsers = filteredUsers.filter(user => user.isActive === isActive)
    }

    const paginatedUsers = filteredUsers.slice(offset, offset + limit)

    return HttpResponse.json({
      result: {
        data: {
          users: paginatedUsers,
          total: filteredUsers.length,
          hasMore: offset + limit < filteredUsers.length,
        },
      },
    })
  }),

  // Get user by ID
  http.get('/api/trpc/users.getById', ({ request }) => {
    const url = new URL(request.url)
    const input = JSON.parse(url.searchParams.get('input') || '{}')
    const { id } = input

    const user = mockUsers.find(u => u.id === id)

    if (!user) {
      return HttpResponse.json(
        {
          error: {
            message: 'User not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    return HttpResponse.json({
      result: {
        data: user,
      },
    })
  }),

  // Update user
  http.patch('/api/trpc/users.update', async ({ request }) => {
    const body = await request.json() as any
    const { id, ...updateData } = body

    const userIndex = mockUsers.findIndex(u => u.id === id)

    if (userIndex === -1) {
      return HttpResponse.json(
        {
          error: {
            message: 'User not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    const updatedUser = {
      ...mockUsers[userIndex],
      ...updateData,
      updatedAt: new Date().toISOString(),
    }

    mockUsers[userIndex] = updatedUser

    return HttpResponse.json({
      result: {
        data: updatedUser,
      },
    })
  }),

  // Update user role
  http.patch('/api/trpc/users.updateRole', async ({ request }) => {
    const body = await request.json() as any
    const { userId, role } = body

    const userIndex = mockUsers.findIndex(u => u.id === userId)

    if (userIndex === -1) {
      return HttpResponse.json(
        {
          error: {
            message: 'User not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    const updatedUser = {
      ...mockUsers[userIndex],
      role,
      updatedAt: new Date().toISOString(),
    }

    mockUsers[userIndex] = updatedUser

    return HttpResponse.json({
      result: {
        data: updatedUser,
      },
    })
  }),

  // Deactivate user
  http.patch('/api/trpc/users.deactivate', async ({ request }) => {
    const body = await request.json() as any
    const { userId } = body

    const userIndex = mockUsers.findIndex(u => u.id === userId)

    if (userIndex === -1) {
      return HttpResponse.json(
        {
          error: {
            message: 'User not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    const updatedUser = {
      ...mockUsers[userIndex],
      isActive: false,
      updatedAt: new Date().toISOString(),
    }

    mockUsers[userIndex] = updatedUser

    return HttpResponse.json({
      result: {
        data: updatedUser,
      },
    })
  }),

  // Activate user
  http.patch('/api/trpc/users.activate', async ({ request }) => {
    const body = await request.json() as any
    const { userId } = body

    const userIndex = mockUsers.findIndex(u => u.id === userId)

    if (userIndex === -1) {
      return HttpResponse.json(
        {
          error: {
            message: 'User not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    const updatedUser = {
      ...mockUsers[userIndex],
      isActive: true,
      updatedAt: new Date().toISOString(),
    }

    mockUsers[userIndex] = updatedUser

    return HttpResponse.json({
      result: {
        data: updatedUser,
      },
    })
  }),

  // Delete user
  http.delete('/api/trpc/users.delete', ({ request }) => {
    const url = new URL(request.url)
    const input = JSON.parse(url.searchParams.get('input') || '{}')
    const { userId } = input

    const userIndex = mockUsers.findIndex(u => u.id === userId)

    if (userIndex === -1) {
      return HttpResponse.json(
        {
          error: {
            message: 'User not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    mockUsers.splice(userIndex, 1)

    return HttpResponse.json({
      result: {
        data: { success: true },
      },
    })
  }),
]
