'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { trpc } from '@/lib/trpc'
import { Button, Input, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@clm/ui'

interface ContractParty {
  name: string
  email: string
  phone: string
  address: string
  role: 'CLIENT' | 'VENDOR' | 'PARTNER' | 'INTERNAL'
}

export default function NewContractPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  // Form state
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [priority, setPriority] = useState<'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'>('MEDIUM')
  const [value, setValue] = useState('')
  const [currency, setCurrency] = useState('IDR')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [parties, setParties] = useState<ContractParty[]>([
    { name: '', email: '', phone: '', address: '', role: 'CLIENT' }
  ])

  const createContractMutation = trpc.contracts.create.useMutation({
    onSuccess: (data) => {
      router.push(`/dashboard/contracts/${data.id}`)
    },
    onError: (error) => {
      setError(error.message)
      setIsLoading(false)
    },
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const contractData = {
        title,
        description: description || undefined,
        priority,
        value: value ? parseFloat(value) : undefined,
        currency,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        parties: parties.filter(party => party.name.trim() !== ''),
      }

      await createContractMutation.mutateAsync(contractData)
    } catch (err) {
      // Error handled by mutation
    }
  }

  const addParty = () => {
    setParties([...parties, { name: '', email: '', phone: '', address: '', role: 'CLIENT' }])
  }

  const removeParty = (index: number) => {
    setParties(parties.filter((_, i) => i !== index))
  }

  const updateParty = (index: number, field: keyof ContractParty, value: string) => {
    const updatedParties = [...parties]
    updatedParties[index] = { ...updatedParties[index], [field]: value }
    setParties(updatedParties)
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
        >
          ← Kembali
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Buat Kontrak Baru</h1>
          <p className="text-gray-600">Isi informasi kontrak yang akan dibuat</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Dasar</CardTitle>
            <CardDescription>
              Informasi umum tentang kontrak
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Judul Kontrak *
              </label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Masukkan judul kontrak"
                required
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Deskripsi
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Deskripsi singkat tentang kontrak"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
                  Prioritas
                </label>
                <select
                  id="priority"
                  value={priority}
                  onChange={(e) => setPriority(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="LOW">Rendah</option>
                  <option value="MEDIUM">Sedang</option>
                  <option value="HIGH">Tinggi</option>
                  <option value="URGENT">Mendesak</option>
                </select>
              </div>

              <div>
                <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-1">
                  Mata Uang
                </label>
                <select
                  id="currency"
                  value={currency}
                  onChange={(e) => setCurrency(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="IDR">Rupiah (IDR)</option>
                  <option value="USD">US Dollar (USD)</option>
                  <option value="EUR">Euro (EUR)</option>
                  <option value="SGD">Singapore Dollar (SGD)</option>
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="value" className="block text-sm font-medium text-gray-700 mb-1">
                Nilai Kontrak
              </label>
              <Input
                id="value"
                type="number"
                value={value}
                onChange={(e) => setValue(e.target.value)}
                placeholder="0"
                min="0"
                step="0.01"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Tanggal Mulai
                </label>
                <Input
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>

              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Tanggal Berakhir
                </label>
                <Input
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  min={startDate}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Parties */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Pihak Terlibat</CardTitle>
                <CardDescription>
                  Tambahkan pihak-pihak yang terlibat dalam kontrak
                </CardDescription>
              </div>
              <Button type="button" variant="outline" onClick={addParty}>
                + Tambah Pihak
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {parties.map((party, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Pihak {index + 1}</h4>
                  {parties.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeParty(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      Hapus
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nama *
                    </label>
                    <Input
                      value={party.name}
                      onChange={(e) => updateParty(index, 'name', e.target.value)}
                      placeholder="Nama pihak"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Role
                    </label>
                    <select
                      value={party.role}
                      onChange={(e) => updateParty(index, 'role', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="CLIENT">Klien</option>
                      <option value="VENDOR">Vendor</option>
                      <option value="PARTNER">Partner</option>
                      <option value="INTERNAL">Internal</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email
                    </label>
                    <Input
                      type="email"
                      value={party.email}
                      onChange={(e) => updateParty(index, 'email', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Telepon
                    </label>
                    <Input
                      value={party.phone}
                      onChange={(e) => updateParty(index, 'phone', e.target.value)}
                      placeholder="+62 xxx xxx xxxx"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Alamat
                  </label>
                  <textarea
                    value={party.address}
                    onChange={(e) => updateParty(index, 'address', e.target.value)}
                    placeholder="Alamat lengkap"
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isLoading}
          >
            Batal
          </Button>
          <Button
            type="submit"
            disabled={isLoading || !title.trim()}
          >
            {isLoading ? 'Membuat...' : 'Buat Kontrak'}
          </Button>
        </div>
      </form>
    </div>
  )
}
