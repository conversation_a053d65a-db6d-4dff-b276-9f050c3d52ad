import { opensearchClient } from './opensearch';

export async function searchContracts(query: string) {
  const response = await opensearchClient.search({
    index: 'contracts',
    body: {
      query: {
        multi_match: {
          query,
          fields: ['attachment.content', 'title', 'description'],
          fuzziness: 'AUTO',
        },
      },
    },
  });

  return response.body.hits.hits;
}
