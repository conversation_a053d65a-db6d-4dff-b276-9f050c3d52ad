'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useState } from 'react'
import { trpc } from '@/lib/trpc'
import { usePermissions } from '@clm/auth'
import { <PERSON><PERSON>, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@clm/ui'

// A simple component to display the results of AI actions
function ActionResult({ title, data, isLoading, error }: any) {
  if (!data && !isLoading && !error) return null;

  return (
    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
      <h4 className="font-medium text-gray-800 mb-2">{title}</h4>
      {isLoading && <p className="text-sm text-gray-500">Loading...</p>}
      {error && <p className="text-sm text-red-600">Error: {error.message}</p>}
      {data && <pre className="text-sm text-gray-700 whitespace-pre-wrap">{JSON.stringify(data, null, 2)}</pre>}
    </div>
  );
}

export default function ContractDetailPage() {
  const params = useParams()
  const router = useRouter()
  const contractId = params?.id as string
  const permissions = usePermissions()
  const [activeTab, setActiveTab] = useState('overview')

  const { data: contract, isLoading, error } = trpc.contracts.getById.useQuery({
    id: contractId,
  })

  // --- tRPC Mutations for new features ---
  // TODO: Uncomment when procedures are available
  // const summarizeMutation = trpc.contracts.summarize.useMutation();
  // const extractMetadataMutation = trpc.contracts.extractMetadata.useMutation();
  // const analyzeRiskMutation = trpc.contracts.analyzeRisk.useMutation();
  // const signMutation = trpc.contracts.sign.useMutation();
  // const applyMeteraiMutation = trpc.contracts.applyMeterai.useMutation();

  const handleAction = (mutation: any) => {
    mutation.mutate({ contractId });
  }

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
            <div className="space-y-6">
              <div className="h-32 bg-gray-200 rounded"></div>
              <div className="h-48 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !contract) {
    return (
      <div className="max-w-6xl mx-auto">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <div className="text-6xl mb-4">❌</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Kontrak tidak ditemukan
              </h3>
              <p className="text-gray-500 mb-6">
                Kontrak yang Anda cari tidak ada atau telah dihapus.
              </p>
              <Button onClick={() => router.push('/dashboard/contracts')}>
                ← Kembali ke Daftar Kontrak
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'bg-gray-100 text-gray-800'
      case 'REVIEW': return 'bg-yellow-100 text-yellow-800'
      case 'APPROVED': return 'bg-green-100 text-green-800'
      case 'SIGNED': return 'bg-blue-100 text-blue-800'
      case 'ACTIVE': return 'bg-emerald-100 text-emerald-800'
      case 'EXPIRED': return 'bg-red-100 text-red-800'
      case 'TERMINATED': return 'bg-red-100 text-red-800'
      case 'REJECTED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number, currency: string = 'IDR') => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency,
    }).format(amount)
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📋' },
    { id: 'parties', label: 'Pihak Terlibat', icon: '👥' },
    { id: 'workflow', label: 'Workflow', icon: '🔄' },
    { id: 'comments', label: 'Komentar', icon: '💬' },
    { id: 'versions', label: 'Versi', icon: '📝' },
    { id: 'files', label: 'File', icon: '📎' },
  ]

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/contracts')}
          >
            ← Kembali
          </Button>
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl font-bold text-gray-900">
                {contract.title}
              </h1>
              <span className={`px-3 py-1 text-sm rounded-full ${getStatusColor(contract.status)}`}>
                {contract.status}
              </span>
            </div>
            <p className="text-gray-600">
              Dibuat oleh {contract.creator.name} • {new Date(contract.createdAt).toLocaleDateString('id-ID')}
            </p>
          </div>
        </div>

        <div className="flex space-x-2">
          {permissions.canEditContract(contract.createdBy) && (
            <Button variant="outline">
              ✏️ Edit
            </Button>
          )}
          {permissions.canApproveContracts() && contract.status === 'REVIEW' && (
            <>
              <Button variant="outline" className="text-green-600">
                ✅ Setujui
              </Button>
              <Button variant="outline" className="text-red-600">
                ❌ Tolak
              </Button>
            </>
          )}
          <Button>
            📤 Bagikan
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
              {tab.id === 'comments' && contract._count.comments > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                  {contract._count.comments}
                </span>
              )}
              {tab.id === 'versions' && contract._count.versions > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                  {contract._count.versions}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {activeTab === 'overview' && (
            <Card>
              <CardHeader>
                <CardTitle>Detail Kontrak</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {contract.description && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Deskripsi</h4>
                    <p className="text-gray-600">{contract.description}</p>
                  </div>
                )}

                {contract.content && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Konten Kontrak</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <pre className="whitespace-pre-wrap text-sm text-gray-700">
                        {contract.content}
                      </pre>
                    </div>
                  </div>
                )}

                {!contract.content && (
                  <div className="text-center py-8 bg-gray-50 rounded-lg">
                    <div className="text-4xl mb-2">📄</div>
                    <p className="text-gray-500">Konten kontrak belum ditambahkan</p>
                    {permissions.canEditContract(contract.createdBy) && (
                      <Button variant="outline" className="mt-2">
                        Tambah Konten
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {activeTab === 'parties' && (
            <Card>
              <CardHeader>
                <CardTitle>Pihak Terlibat</CardTitle>
                <CardDescription>
                  Daftar semua pihak yang terlibat dalam kontrak ini
                </CardDescription>
              </CardHeader>
              <CardContent>
                {contract.parties.length > 0 ? (
                  <div className="space-y-4">
                    {contract.parties.map((party) => (
                      <div key={party.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium text-gray-900">{party.name}</h4>
                            <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded mt-1">
                              {party.role}
                            </span>
                          </div>
                        </div>
                        <div className="mt-3 space-y-1 text-sm text-gray-600">
                          {party.email && (
                            <p>📧 {party.email}</p>
                          )}
                          {party.phone && (
                            <p>📞 {party.phone}</p>
                          )}
                          {party.address && (
                            <p>📍 {party.address}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-2">👥</div>
                    <p className="text-gray-500">Belum ada pihak yang ditambahkan</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {activeTab === 'workflow' && (
            <Card>
              <CardHeader>
                <CardTitle>Workflow Persetujuan</CardTitle>
              </CardHeader>
              <CardContent>
                {contract.workflowSteps.length > 0 ? (
                  <div className="space-y-4">
                    {contract.workflowSteps.map((step, index) => (
                      <div key={step.id} className="flex items-start space-x-4">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                          step.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                          step.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                          step.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-600'
                        }`}>
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{step.name}</h4>
                          {step.description && (
                            <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                          )}
                          {step.assignee && (
                            <p className="text-sm text-gray-500 mt-1">
                              Ditugaskan ke: {step.assignee.name}
                            </p>
                          )}
                          <span className={`inline-block px-2 py-1 text-xs rounded mt-2 ${
                            step.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                            step.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                            step.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-600'
                          }`}>
                            {step.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-2">🔄</div>
                    <p className="text-gray-500">Belum ada workflow yang dikonfigurasi</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {activeTab === 'comments' && (
            <Card>
              <CardHeader>
                <CardTitle>Komentar & Diskusi</CardTitle>
              </CardHeader>
              <CardContent>
                {contract.comments.length > 0 ? (
                  <div className="space-y-4">
                    {contract.comments.map((comment) => (
                      <div key={comment.id} className="border-l-4 border-blue-200 pl-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                            {comment.author.name.charAt(0)}
                          </div>
                          <span className="font-medium text-sm">{comment.author.name}</span>
                          <span className="text-xs text-gray-500">
                            {new Date(comment.createdAt).toLocaleDateString('id-ID')}
                          </span>
                        </div>
                        <p className="text-gray-700">{comment.content}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-2">💬</div>
                    <p className="text-gray-500">Belum ada komentar</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contract Info */}
          <Card>
            <CardHeader>
              <CardTitle>Informasi Kontrak</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <p className="mt-1">
                  <span className={`px-2 py-1 text-sm rounded ${getStatusColor(contract.status)}`}>
                    {contract.status}
                  </span>
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Prioritas</label>
                <p className="mt-1 text-sm text-gray-900">{contract.priority}</p>
              </div>

              {contract.value && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Nilai Kontrak</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {formatCurrency(contract.value, contract.currency)}
                  </p>
                </div>
              )}

              {contract.startDate && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Tanggal Mulai</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(contract.startDate).toLocaleDateString('id-ID')}
                  </p>
                </div>
              )}

              {contract.endDate && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Tanggal Berakhir</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(contract.endDate).toLocaleDateString('id-ID')}
                  </p>
                </div>
              )}

              <div>
                <label className="text-sm font-medium text-gray-500">Dibuat</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(contract.createdAt).toLocaleDateString('id-ID')}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Terakhir Diperbarui</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(contract.updatedAt).toLocaleDateString('id-ID')}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* --- NEW ACTION PANEL --- */}
          <Card>
            <CardHeader>
              <CardTitle>AI & Compliance Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => handleAction(summarizeMutation)}
                disabled={summarizeMutation.isLoading}
              >
                🤖 Summarize with AI
              </Button>
              <ActionResult title="AI Summary" {...summarizeMutation} />

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => handleAction(extractMetadataMutation)}
                disabled={extractMetadataMutation.isLoading}
              >
                📊 Extract Metadata
              </Button>
              <ActionResult title="Extracted Metadata" {...extractMetadataMutation} />

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => handleAction(analyzeRiskMutation)}
                disabled={analyzeRiskMutation.isLoading}
              >
                🔬 Analyze Risk
              </Button>
              <ActionResult title="Risk Analysis" {...analyzeRiskMutation} />

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => signMutation.mutate({ contractId, recipient: { name: 'Test User', email: '<EMAIL>' } })}
                disabled={signMutation.isLoading}
              >
                ✍️ Request Signature
              </Button>
              <ActionResult title="Signature Status" {...signMutation} />

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => handleAction(applyMeteraiMutation)}
                disabled={applyMeteraiMutation.isLoading}
              >
                 Stamps Apply e-Meterai
              </Button>
              <ActionResult title="e-Meterai Status" {...applyMeteraiMutation} />
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Aksi Cepat</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                📧 Kirim Email
              </Button>
              <Button variant="outline" className="w-full justify-start">
                📄 Export PDF
              </Button>
              <Button variant="outline" className="w-full justify-start">
                📋 Duplikasi
              </Button>
              <Button variant="outline" className="w-full justify-start">
                📊 Lihat Audit Trail
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
