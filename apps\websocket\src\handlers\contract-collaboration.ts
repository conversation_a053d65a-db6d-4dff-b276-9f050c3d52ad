import { Server, Socket } from 'socket.io'
import { z } from 'zod'
import { sessionStore } from '@clm/lib'

interface ContractRoom {
  contractId: string
  participants: Set<string> // socket IDs
  cursors: Map<string, CursorPosition>
  selections: Map<string, TextSelection>
  lastActivity: number
}

interface CursorPosition {
  userId: string
  userName: string
  position: number
  timestamp: number
}

interface TextSelection {
  userId: string
  userName: string
  start: number
  end: number
  timestamp: number
}

const joinContractSchema = z.object({
  contractId: z.string(),
})

const cursorUpdateSchema = z.object({
  contractId: z.string(),
  position: z.number(),
})

const selectionUpdateSchema = z.object({
  contractId: z.string(),
  start: z.number(),
  end: z.number(),
})

const textChangeSchema = z.object({
  contractId: z.string(),
  operation: z.object({
    type: z.enum(['insert', 'delete', 'replace']),
    position: z.number(),
    content: z.string().optional(),
    length: z.number().optional(),
  }),
  version: z.number(),
})

export class ContractCollaborationHandler {
  private rooms = new Map<string, ContractRoom>()

  constructor(private io: Server) {}

  handleConnection(socket: Socket) {
    const user = socket.data.user

    // Join contract room
    socket.on('contract:join', (data) => {
      try {
        const { contractId } = joinContractSchema.parse(data)
        this.joinContractRoom(socket, contractId)
      } catch (error) {
        socket.emit('error', { message: 'Invalid contract join data' })
      }
    })

    // Leave contract room
    socket.on('contract:leave', (data) => {
      try {
        const { contractId } = joinContractSchema.parse(data)
        this.leaveContractRoom(socket, contractId)
      } catch (error) {
        socket.emit('error', { message: 'Invalid contract leave data' })
      }
    })

    // Cursor position updates
    socket.on('contract:cursor', (data) => {
      try {
        const { contractId, position } = cursorUpdateSchema.parse(data)
        this.updateCursor(socket, contractId, position)
      } catch (error) {
        socket.emit('error', { message: 'Invalid cursor data' })
      }
    })

    // Text selection updates
    socket.on('contract:selection', (data) => {
      try {
        const { contractId, start, end } = selectionUpdateSchema.parse(data)
        this.updateSelection(socket, contractId, start, end)
      } catch (error) {
        socket.emit('error', { message: 'Invalid selection data' })
      }
    })

    // Text changes (operational transformation)
    socket.on('contract:text-change', (data) => {
      try {
        const textChange = textChangeSchema.parse(data)
        this.handleTextChange(socket, textChange)
      } catch (error) {
        socket.emit('error', { message: 'Invalid text change data' })
      }
    })

    // Typing indicators
    socket.on('contract:typing-start', (data) => {
      try {
        const { contractId } = joinContractSchema.parse(data)
        this.handleTypingStart(socket, contractId)
      } catch (error) {
        socket.emit('error', { message: 'Invalid typing data' })
      }
    })

    socket.on('contract:typing-stop', (data) => {
      try {
        const { contractId } = joinContractSchema.parse(data)
        this.handleTypingStop(socket, contractId)
      } catch (error) {
        socket.emit('error', { message: 'Invalid typing data' })
      }
    })
  }

  handleDisconnection(socket: Socket) {
    // Remove from all contract rooms
    for (const [contractId, room] of this.rooms.entries()) {
      if (room.participants.has(socket.id)) {
        this.leaveContractRoom(socket, contractId)
      }
    }
  }

  private joinContractRoom(socket: Socket, contractId: string) {
    const user = socket.data.user
    const roomKey = `contract:${contractId}`

    // Join socket room
    socket.join(roomKey)
    sessionStore.joinRoom(socket.id, roomKey)

    // Get or create contract room
    if (!this.rooms.has(contractId)) {
      this.rooms.set(contractId, {
        contractId,
        participants: new Set(),
        cursors: new Map(),
        selections: new Map(),
        lastActivity: Date.now(),
      })
    }

    const room = this.rooms.get(contractId)!
    room.participants.add(socket.id)
    room.lastActivity = Date.now()

    // Notify others about new participant
    socket.to(roomKey).emit('contract:user-joined', {
      userId: user.id,
      userName: user.name,
      avatar: user.avatar,
      timestamp: Date.now(),
    })

    // Send current state to new participant
    socket.emit('contract:joined', {
      contractId,
      participants: Array.from(room.participants).map(socketId => {
        const wsSession = sessionStore.getWebSocketSession(socketId)
        return wsSession ? {
          userId: wsSession.userId,
          userName: wsSession.name,
          avatar: wsSession.avatar,
        } : null
      }).filter(Boolean),
      cursors: Array.from(room.cursors.values()),
      selections: Array.from(room.selections.values()),
    })

    console.log(`User ${user.name} joined contract ${contractId}`)
  }

  private leaveContractRoom(socket: Socket, contractId: string) {
    const user = socket.data.user
    const roomKey = `contract:${contractId}`

    // Leave socket room
    socket.leave(roomKey)
    sessionStore.leaveRoom(socket.id, roomKey)

    const room = this.rooms.get(contractId)
    if (room) {
      room.participants.delete(socket.id)
      room.cursors.delete(socket.id)
      room.selections.delete(socket.id)

      // Notify others about participant leaving
      socket.to(roomKey).emit('contract:user-left', {
        userId: user.id,
        userName: user.name,
        timestamp: Date.now(),
      })

      // Clean up empty rooms
      if (room.participants.size === 0) {
        this.rooms.delete(contractId)
      }
    }

    console.log(`User ${user.name} left contract ${contractId}`)
  }

  private updateCursor(socket: Socket, contractId: string, position: number) {
    const user = socket.data.user
    const room = this.rooms.get(contractId)
    
    if (!room || !room.participants.has(socket.id)) {
      return
    }

    const cursor: CursorPosition = {
      userId: user.id,
      userName: user.name,
      position,
      timestamp: Date.now(),
    }

    room.cursors.set(socket.id, cursor)
    room.lastActivity = Date.now()

    // Broadcast cursor update to other participants
    socket.to(`contract:${contractId}`).emit('contract:cursor-update', cursor)
  }

  private updateSelection(socket: Socket, contractId: string, start: number, end: number) {
    const user = socket.data.user
    const room = this.rooms.get(contractId)
    
    if (!room || !room.participants.has(socket.id)) {
      return
    }

    const selection: TextSelection = {
      userId: user.id,
      userName: user.name,
      start,
      end,
      timestamp: Date.now(),
    }

    room.selections.set(socket.id, selection)
    room.lastActivity = Date.now()

    // Broadcast selection update to other participants
    socket.to(`contract:${contractId}`).emit('contract:selection-update', selection)
  }

  private handleTextChange(socket: Socket, textChange: any) {
    const user = socket.data.user
    const { contractId } = textChange
    const room = this.rooms.get(contractId)
    
    if (!room || !room.participants.has(socket.id)) {
      return
    }

    room.lastActivity = Date.now()

    // Add user info to the change
    const changeWithUser = {
      ...textChange,
      userId: user.id,
      userName: user.name,
      timestamp: Date.now(),
    }

    // Broadcast text change to other participants
    socket.to(`contract:${contractId}`).emit('contract:text-changed', changeWithUser)

    // TODO: Implement operational transformation for conflict resolution
    // TODO: Save changes to database
  }

  private handleTypingStart(socket: Socket, contractId: string) {
    const user = socket.data.user
    const room = this.rooms.get(contractId)
    
    if (!room || !room.participants.has(socket.id)) {
      return
    }

    socket.to(`contract:${contractId}`).emit('contract:typing-start', {
      userId: user.id,
      userName: user.name,
      timestamp: Date.now(),
    })
  }

  private handleTypingStop(socket: Socket, contractId: string) {
    const user = socket.data.user
    const room = this.rooms.get(contractId)
    
    if (!room || !room.participants.has(socket.id)) {
      return
    }

    socket.to(`contract:${contractId}`).emit('contract:typing-stop', {
      userId: user.id,
      userName: user.name,
      timestamp: Date.now(),
    })
  }

  // Get room statistics
  getRoomStats() {
    return {
      totalRooms: this.rooms.size,
      rooms: Array.from(this.rooms.entries()).map(([contractId, room]) => ({
        contractId,
        participants: room.participants.size,
        lastActivity: room.lastActivity,
      })),
    }
  }

  // Clean up inactive rooms
  cleanupInactiveRooms(maxInactiveTime = 30 * 60 * 1000) { // 30 minutes
    const now = Date.now()
    const inactiveRooms: string[] = []

    for (const [contractId, room] of this.rooms.entries()) {
      if (now - room.lastActivity > maxInactiveTime && room.participants.size === 0) {
        inactiveRooms.push(contractId)
      }
    }

    inactiveRooms.forEach(contractId => {
      this.rooms.delete(contractId)
    })

    return inactiveRooms.length
  }
}
