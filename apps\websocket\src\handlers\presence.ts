import { Server, Socket } from 'socket.io'
import { sessionStore } from '@clm/lib'

interface UserPresence {
  userId: string
  userName: string
  avatar?: string
  status: 'online' | 'away' | 'busy' | 'offline'
  lastSeen: number
  currentPage?: string
  socketIds: Set<string>
}

export class PresenceHandler {
  private userPresence = new Map<string, UserPresence>()

  constructor(private io: Server) {
    // Clean up offline users periodically
    setInterval(() => {
      this.cleanupOfflineUsers()
    }, 60000) // Every minute
  }

  handleConnection(socket: Socket) {
    const user = socket.data.user

    // Update user presence
    this.updateUserPresence(user.id, {
      userId: user.id,
      userName: user.name,
      avatar: user.avatar,
      status: 'online',
      lastSeen: Date.now(),
      socketIds: new Set([socket.id]),
    })

    // Broadcast user online status
    this.broadcastPresenceUpdate(user.id, 'online')

    // Handle status updates
    socket.on('presence:status', (data) => {
      const { status } = data
      if (['online', 'away', 'busy'].includes(status)) {
        this.updateUserStatus(user.id, status)
        this.broadcastPresenceUpdate(user.id, status)
      }
    })

    // Handle page/location updates
    socket.on('presence:page', (data) => {
      const { page } = data
      this.updateUserPage(user.id, page)
    })

    // Handle heartbeat
    socket.on('presence:heartbeat', () => {
      this.updateUserLastSeen(user.id)
    })

    // Get online users
    socket.on('presence:get-online', () => {
      const onlineUsers = this.getOnlineUsers()
      socket.emit('presence:online-users', { users: onlineUsers })
    })

    // Get user presence
    socket.on('presence:get-user', (data) => {
      const { userId } = data
      const presence = this.getUserPresence(userId)
      socket.emit('presence:user-presence', { userId, presence })
    })
  }

  handleDisconnection(socket: Socket) {
    const user = socket.data.user
    const presence = this.userPresence.get(user.id)

    if (presence) {
      presence.socketIds.delete(socket.id)
      presence.lastSeen = Date.now()

      // If no more sockets for this user, mark as offline
      if (presence.socketIds.size === 0) {
        presence.status = 'offline'
        this.broadcastPresenceUpdate(user.id, 'offline')
      }
    }
  }

  private updateUserPresence(userId: string, presenceData: Partial<UserPresence>) {
    const existing = this.userPresence.get(userId)

    if (existing) {
      // Merge socket IDs
      if (presenceData.socketIds) {
        presenceData.socketIds.forEach(id => existing.socketIds.add(id))
        delete presenceData.socketIds
      }

      Object.assign(existing, presenceData)
    } else {
      this.userPresence.set(userId, {
        userId,
        userName: '',
        status: 'offline',
        lastSeen: Date.now(),
        socketIds: new Set(),
        ...presenceData,
      } as UserPresence)
    }
  }

  private updateUserStatus(userId: string, status: 'online' | 'away' | 'busy' | 'offline') {
    const presence = this.userPresence.get(userId)
    if (presence) {
      presence.status = status
      presence.lastSeen = Date.now()
    }
  }

  private updateUserPage(userId: string, page: string) {
    const presence = this.userPresence.get(userId)
    if (presence) {
      presence.currentPage = page
      presence.lastSeen = Date.now()
    }
  }

  private updateUserLastSeen(userId: string) {
    const presence = this.userPresence.get(userId)
    if (presence) {
      presence.lastSeen = Date.now()
    }
  }

  private broadcastPresenceUpdate(userId: string, status: string) {
    const presence = this.userPresence.get(userId)
    if (presence) {
      this.io.emit('presence:user-update', {
        userId,
        userName: presence.userName,
        avatar: presence.avatar,
        status,
        lastSeen: presence.lastSeen,
        currentPage: presence.currentPage,
      })
    }
  }

  private getOnlineUsers() {
    const onlineUsers: any[] = []

    for (const [userId, presence] of this.userPresence.entries()) {
      if (presence.status !== 'offline' && presence.socketIds.size > 0) {
        onlineUsers.push({
          userId,
          userName: presence.userName,
          avatar: presence.avatar,
          status: presence.status,
          lastSeen: presence.lastSeen,
          currentPage: presence.currentPage,
        })
      }
    }

    return onlineUsers
  }

  private getUserPresence(userId: string) {
    const presence = this.userPresence.get(userId)
    if (!presence) return null

    return {
      userId,
      userName: presence.userName,
      avatar: presence.avatar,
      status: presence.status,
      lastSeen: presence.lastSeen,
      currentPage: presence.currentPage,
      isOnline: presence.status !== 'offline' && presence.socketIds.size > 0,
    }
  }

  private cleanupOfflineUsers() {
    const now = Date.now()
    const offlineThreshold = 5 * 60 * 1000 // 5 minutes

    for (const [userId, presence] of this.userPresence.entries()) {
      // Mark users as offline if they haven't been seen for a while
      if (presence.status !== 'offline' && 
          now - presence.lastSeen > offlineThreshold) {
        presence.status = 'offline'
        this.broadcastPresenceUpdate(userId, 'offline')
      }

      // Remove completely inactive users
      if (presence.status === 'offline' && 
          presence.socketIds.size === 0 && 
          now - presence.lastSeen > 24 * 60 * 60 * 1000) { // 24 hours
        this.userPresence.delete(userId)
      }
    }
  }

  // Get presence statistics
  getPresenceStats() {
    const stats = {
      totalUsers: this.userPresence.size,
      online: 0,
      away: 0,
      busy: 0,
      offline: 0,
    }

    for (const presence of this.userPresence.values()) {
      stats[presence.status]++
    }

    return stats
  }

  // Get users by status
  getUsersByStatus(status: 'online' | 'away' | 'busy' | 'offline') {
    const users: any[] = []

    for (const presence of this.userPresence.values()) {
      if (presence.status === status) {
        users.push({
          userId: presence.userId,
          userName: presence.userName,
          avatar: presence.avatar,
          lastSeen: presence.lastSeen,
          currentPage: presence.currentPage,
        })
      }
    }

    return users
  }

  // Check if user is online
  isUserOnline(userId: string): boolean {
    const presence = this.userPresence.get(userId)
    return presence ? 
      presence.status !== 'offline' && presence.socketIds.size > 0 : 
      false
  }

  // Get users in specific page/location
  getUsersInPage(page: string) {
    const users: any[] = []

    for (const presence of this.userPresence.values()) {
      if (presence.currentPage === page && presence.status !== 'offline') {
        users.push({
          userId: presence.userId,
          userName: presence.userName,
          avatar: presence.avatar,
          status: presence.status,
          lastSeen: presence.lastSeen,
        })
      }
    }

    return users
  }

  // Broadcast message to online users
  broadcastToOnlineUsers(event: string, data: any) {
    for (const presence of this.userPresence.values()) {
      if (presence.status !== 'offline' && presence.socketIds.size > 0) {
        presence.socketIds.forEach(socketId => {
          this.io.to(socketId).emit(event, data)
        })
      }
    }
  }

  // Send message to specific user if online
  sendToUserIfOnline(userId: string, event: string, data: any): boolean {
    const presence = this.userPresence.get(userId)
    if (presence && presence.status !== 'offline' && presence.socketIds.size > 0) {
      presence.socketIds.forEach(socketId => {
        this.io.to(socketId).emit(event, data)
      })
      return true
    }
    return false
  }
}
