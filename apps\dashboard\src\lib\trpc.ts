import { createTRPCNext } from '@trpc/next'
import { httpBatchLink } from '@trpc/client'
import { type AppRouter } from '@clm/api'
import superjson from 'superjson'

function getBaseUrl() {
  if (typeof window !== 'undefined') return '' // browser should use relative url
  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}` // SSR should use vercel url
  return `http://localhost:${process.env.PORT ?? 3000}` // dev SSR should use localhost
}

export const trpc = createTRPCNext<AppRouter>({
  config() {
    return {
      transformer: superjson,
      links: [
        httpBatchLink({
          url: `${getBaseUrl()}/api/trpc`,
          headers() {
            const token = typeof window !== 'undefined' 
              ? localStorage.getItem('auth-token')
              : null
            
            return token ? { authorization: `Bearer ${token}` } : {}
          },
        }),
      ],
    }
  },
  ssr: false,
})
