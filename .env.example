# Database
DATABASE_URL="postgresql://username:password@localhost:5432/clm_db"

# Redis
REDIS_URL="redis://localhost:6379"

# Better Auth
BETTER_AUTH_SECRET="your-super-secret-auth-key-change-this-in-production"
BETTER_AUTH_URL="http://localhost:3000"

# MinIO / S3 Storage
MINIO_ENDPOINT="localhost:9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
MINIO_BUCKET_NAME="clm-documents"
MINIO_USE_SSL="false"

# AI Services
GEMINI_API_KEY="your-gemini-api-key"

# Indonesian Services
PERURI_API_KEY="your-peruri-api-key"
PERURI_API_URL="https://api.peruri.co.id"

# PSrE (Digital Signature Provider)
PSRE_API_KEY="your-psre-api-key"
PSRE_API_URL="https://api.privyid.tech"

# Email Services
EMAIL_PROVIDER="resend" # or "postmark"
RESEND_API_KEY="your-resend-api-key"
POSTMARK_API_KEY="your-postmark-api-key"
EMAIL_FROM="<EMAIL>"

# OpenSearch
OPENSEARCH_URL="https://localhost:9200"
OPENSEARCH_USERNAME="admin"
OPENSEARCH_PASSWORD="admin"

# WebSocket
WEBSOCKET_PORT="3003"
WEBSOCKET_CORS_ORIGIN="http://localhost:3000,http://localhost:3001,http://localhost:3002"

# App Configuration
APP_NAME="CLM Platform Indonesia"
APP_URL="http://localhost:3000"
ADMIN_URL="http://localhost:3001"
LANDING_URL="http://localhost:3002"

# Security
CORS_ORIGIN="http://localhost:3000,http://localhost:3001,http://localhost:3002"
RATE_LIMIT_WINDOW_MS="900000" # 15 minutes
RATE_LIMIT_MAX_REQUESTS="100"

# File Upload
MAX_FILE_SIZE="10485760" # 10MB in bytes
ALLOWED_FILE_TYPES="pdf,doc,docx,txt"

# Environment
NODE_ENV="development"
LOG_LEVEL="debug"
