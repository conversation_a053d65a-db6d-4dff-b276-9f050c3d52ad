import { createAuthClient } from "better-auth/client"
import type { auth } from "./config"

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
  fetchOptions: {
    onError(e) {
      if (e.error.status === 429) {
        console.error("Rate limit exceeded")
      }
    },
  },
})

export const {
  signIn,
  signUp,
  signOut,
  getSession,
  updateUser,
} = authClient

export const useSession = authClient.useSession

// Custom hooks for CLM-specific functionality
import { useAtom } from 'jotai';
import { sessionAtom } from './store';

export const useAuth = () => {
  const [session] = useAtom(sessionAtom);
  return session;
};



// Utility functions
export async function requireAuth() {
  const session = await getSession()
  if (!session.data?.user) {
    throw new Error("Authentication required")
  }
  return session.data
}



// Error handling
export function isAuthError(error: any): boolean {
  return error?.status === 401 || error?.status === 403
}

export function getAuthErrorMessage(error: any): string {
  if (error?.status === 401) {
    return "Authentication required. Please sign in."
  }
  if (error?.status === 403) {
    return "Access denied. You don't have permission to perform this action."
  }
  if (error?.status === 429) {
    return "Too many requests. Please try again later."
  }
  return error?.message || "An authentication error occurred"
}
