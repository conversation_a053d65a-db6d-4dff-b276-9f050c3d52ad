import { lookup } from 'mime-types'
import { nanoid } from 'nanoid'

export interface FileInfo {
  name: string
  size: number
  type: string
  extension: string
}

export function getFileInfo(file: File): FileInfo {
  const extension = getFileExtension(file.name)
  return {
    name: file.name,
    size: file.size,
    type: file.type || lookup(extension) || 'application/octet-stream',
    extension,
  }
}

export function getFileExtension(fileName: string): string {
  return fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
}

export function generateFileName(originalName: string, prefix?: string): string {
  const extension = getFileExtension(originalName)
  const timestamp = Date.now()
  const randomId = nanoid(8)
  const prefixPart = prefix ? `${prefix}_` : ''
  
  return `${prefixPart}${timestamp}_${randomId}.${extension}`
}

export function isValidFileType(fileName: string, allowedTypes: string[]): boolean {
  const extension = getFileExtension(fileName)
  return allowedTypes.includes(extension)
}

export function isDocumentFile(fileName: string): boolean {
  const documentTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf']
  return isValidFileType(fileName, documentTypes)
}

export function isImageFile(fileName: string): boolean {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
  return isValidFileType(fileName, imageTypes)
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function createFileDownloadUrl(blob: Blob, fileName: string): string {
  const url = URL.createObjectURL(blob)
  return url
}

export function downloadFile(url: string, fileName: string): void {
  if (typeof document === 'undefined') {
    console.warn('downloadFile can only be used in a browser environment.')
    return
  }
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// File upload helpers
export function createFormData(file: File, additionalData?: Record<string, string>): FormData {
  const formData = new FormData()
  formData.append('file', file)
  
  if (additionalData) {
    Object.entries(additionalData).forEach(([key, value]) => {
      formData.append(key, value)
    })
  }
  
  return formData
}
