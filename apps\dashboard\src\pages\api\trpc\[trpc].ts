import { createNextApiHandler } from '@trpc/server/adapters/next'
import { appRouter } from '@clm/api'
import { createContext } from '@clm/api/src/middleware/security'

export default createNextApiHandler({
  router: appRouter,
  createContext: ({ req, res }) => createContext(req),
  onError:
    process.env.NODE_ENV === 'development'
      ? ({ path, error }) => {
          console.error(
            `❌ tRPC failed on ${path ?? '<no-path>'}: ${error.message}`
          )
        }
      : undefined,
})
