import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { auth } from '@clm/auth'

export async function middleware(request: NextRequest) {
  // Only protect dashboard routes
  if (!request.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.next()
  }

  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // Check if user is active
    if (!session.user.isActive) {
      return NextResponse.redirect(new URL('/deactivated', request.url))
    }

    return NextResponse.next()
  } catch (error) {
    console.error('Dashboard middleware error:', error)
    return NextResponse.redirect(new URL('/login', request.url))
  }
}

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/((?!api|_next/static|_next/image|favicon.ico|login|unauthorized|deactivated).*)',
  ],
}
