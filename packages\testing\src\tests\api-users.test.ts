import { describe, it, expect, beforeEach, vi } from 'vitest'
import { TRPCError } from '@trpc/server'
import { usersRouter } from '@clm/api/src/routers/users'
import { mockUsers } from '../utils/test-helpers'

// Mock Prisma
const mockPrisma = {
  user: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    count: vi.fn(),
  },
}

vi.mock('@clm/db', () => ({
  prisma: mockPrisma,
  UserRole: {
    ADMIN: 'ADMIN',
    LEGAL: 'LEGAL',
    FINANCE: 'FINANCE',
    USER: 'USER',
    VIEWER: 'VIEWER',
  },
}))

// Mock bcrypt
vi.mock('bcryptjs', () => ({
  default: {
    hash: vi.fn().mockResolvedValue('hashed-password'),
    compare: vi.fn().mockResolvedValue(true),
  },
}))

// Mock context
const createMockContext = (user = mockUsers.admin) => ({
  user,
  session: { id: 'test-session', userId: user.id },
})

describe('Users Router', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getAll', () => {
    it('should return paginated users for admin', async () => {
      const mockUsersData = [mockUsers.admin, mockUsers.user]
      mockPrisma.user.findMany.mockResolvedValue(mockUsersData)
      mockPrisma.user.count.mockResolvedValue(2)

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      const result = await caller.getAll({
        limit: 10,
        cursor: undefined,
      })

      expect(result.users).toEqual(mockUsersData)
      expect(result.total).toBe(2)
      expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
        take: 11,
        where: {},
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          avatar: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      })
    })

    it('should filter users by role', async () => {
      const mockUsersData = [mockUsers.admin]
      mockPrisma.user.findMany.mockResolvedValue(mockUsersData)
      mockPrisma.user.count.mockResolvedValue(1)

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      await caller.getAll({
        limit: 10,
        role: 'ADMIN',
      })

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            role: 'ADMIN',
          },
        })
      )
    })

    it('should filter users by active status', async () => {
      const mockUsersData = [mockUsers.admin]
      mockPrisma.user.findMany.mockResolvedValue(mockUsersData)
      mockPrisma.user.count.mockResolvedValue(1)

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      await caller.getAll({
        limit: 10,
        isActive: true,
      })

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            isActive: true,
          },
        })
      )
    })

    it('should search users by name and email', async () => {
      const mockUsersData = [mockUsers.admin]
      mockPrisma.user.findMany.mockResolvedValue(mockUsersData)
      mockPrisma.user.count.mockResolvedValue(1)

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      await caller.getAll({
        limit: 10,
        search: 'admin',
      })

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            OR: [
              {
                name: {
                  contains: 'admin',
                  mode: 'insensitive',
                },
              },
              {
                email: {
                  contains: 'admin',
                  mode: 'insensitive',
                },
              },
            ],
          },
        })
      )
    })

    it('should throw error for non-admin users', async () => {
      const caller = usersRouter.createCaller(createMockContext(mockUsers.user))
      
      await expect(caller.getAll({ limit: 10 })).rejects.toThrow(TRPCError)
    })
  })

  describe('getById', () => {
    it('should return user by id for admin', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.user)

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      const result = await caller.getById({ id: 'test-user-id' })

      expect(result).toEqual(mockUsers.user)
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'test-user-id' },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          avatar: true,
        },
      })
    })

    it('should throw error if user not found', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null)

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      await expect(caller.getById({ id: 'non-existent-id' })).rejects.toThrow(
        TRPCError
      )
    })

    it('should throw error for non-admin users', async () => {
      const caller = usersRouter.createCaller(createMockContext(mockUsers.user))
      
      await expect(caller.getById({ id: 'test-user-id' })).rejects.toThrow(
        TRPCError
      )
    })
  })

  describe('create', () => {
    it('should create new user for admin', async () => {
      const newUserData = {
        email: '<EMAIL>',
        name: 'New User',
        password: 'password123',
        role: 'USER' as const,
      }

      mockPrisma.user.findUnique.mockResolvedValue(null) // User doesn't exist
      mockPrisma.user.create.mockResolvedValue({
        ...mockUsers.user,
        ...newUserData,
        id: 'new-user-id',
      })

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      const result = await caller.create(newUserData)

      expect(result.email).toBe(newUserData.email)
      expect(result.name).toBe(newUserData.name)
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: {
          email: newUserData.email,
          name: newUserData.name,
          role: newUserData.role,
          emailVerified: false,
        },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
        },
      })
    })

    it('should throw error if user already exists', async () => {
      const newUserData = {
        email: '<EMAIL>',
        name: 'Existing User',
        password: 'password123',
        role: 'USER' as const,
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.user) // User exists

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      await expect(caller.create(newUserData)).rejects.toThrow(TRPCError)
    })

    it('should validate email format', async () => {
      const invalidUserData = {
        email: 'invalid-email',
        name: 'Test User',
        password: 'password123',
        role: 'USER' as const,
      }

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      await expect(caller.create(invalidUserData)).rejects.toThrow()
    })

    it('should validate password length', async () => {
      const invalidUserData = {
        email: '<EMAIL>',
        name: 'Test User',
        password: '123', // Too short
        role: 'USER' as const,
      }

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      await expect(caller.create(invalidUserData)).rejects.toThrow()
    })

    it('should throw error for non-admin users', async () => {
      const newUserData = {
        email: '<EMAIL>',
        name: 'New User',
        password: 'password123',
        role: 'USER' as const,
      }

      const caller = usersRouter.createCaller(createMockContext(mockUsers.user))
      
      await expect(caller.create(newUserData)).rejects.toThrow(TRPCError)
    })
  })

  describe('update', () => {
    it('should update user for admin', async () => {
      const updateData = {
        name: 'Updated Name',
        role: 'LEGAL' as const,
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.user)
      mockPrisma.user.update.mockResolvedValue({
        ...mockUsers.user,
        ...updateData,
      })

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      const result = await caller.update({
        id: 'test-user-id',
        ...updateData,
      })

      expect(result.name).toBe(updateData.name)
      expect(result.role).toBe(updateData.role)
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'test-user-id' },
        data: updateData,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      })
    })

    it('should throw error if user not found', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null)

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      await expect(caller.update({
        id: 'non-existent-id',
        name: 'Updated Name',
      })).rejects.toThrow(TRPCError)
    })

    it('should throw error for non-admin users', async () => {
      const caller = usersRouter.createCaller(createMockContext(mockUsers.user))
      
      await expect(caller.update({
        id: 'test-user-id',
        name: 'Updated Name',
      })).rejects.toThrow(TRPCError)
    })
  })

  describe('deactivate', () => {
    it('should deactivate user for admin', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.user)
      mockPrisma.user.update.mockResolvedValue({
        ...mockUsers.user,
        isActive: false,
      })

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      const result = await caller.deactivate({ id: 'test-user-id' })

      expect(result.isActive).toBe(false)
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'test-user-id' },
        data: { isActive: false },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      })
    })

    it('should throw error if user not found', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null)

      const caller = usersRouter.createCaller(createMockContext(mockUsers.admin))
      
      await expect(caller.deactivate({ id: 'non-existent-id' })).rejects.toThrow(
        TRPCError
      )
    })

    it('should throw error for non-admin users', async () => {
      const caller = usersRouter.createCaller(createMockContext(mockUsers.user))
      
      await expect(caller.deactivate({ id: 'test-user-id' })).rejects.toThrow(
        TRPCError
      )
    })
  })
})
