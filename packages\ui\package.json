{"name": "@clm/ui", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.400.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "eslint": "^8.57.0", "typescript": "^5.5.3"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}