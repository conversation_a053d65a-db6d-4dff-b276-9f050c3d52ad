<PERSON>tu, ini adalah dokumen persyaratan (requirements document) yang merinci pembuatan platform CLM "Ironclad Versi Indonesia" sesuai dengan tumpukan teknologi spesifik yang Anda minta.

Dokumen ini disusun untuk dijadikan panduan bagi tim developer, produk, dan <PERSON>.

-----

### **Dokumen Persyaratan Proyek: Platform Contract Lifecycle Management (CLM) Indonesia**

**Versi:** 1.0
**Tanggal:** 29 Juli 2025

#### 1\. Ringkasan Eksekutif & Visi Produk

Proyek ini bertujuan untuk membangun sebuah platform *Contract Lifecycle Management* (CLM) modern yang dirancang khusus untuk pasar Indonesia. Platform ini akan mengotomatisasi dan menyederhanakan seluruh siklus hidup kontrak, mulai dari pembuatan, kolaborasi, persetujuan, penandatanganan secara digital, hingga analisis dan penyimpanan.

Visi produk adalah menjadi solusi CLM terdepan di Indonesia dengan menyediakan antarmuka yang intuitif, fitur AI canggih, dan kepatuhan penuh terhadap regulasi Indonesia (UU ITE, e-Meterai, Kedaulatan Data).

Arsitektur akan dibangun menggunakan monorepo (Turborepo) dengan tumpukan teknologi modern berbasis TypeScript (Next.js, tRPC) untuk memastikan pengembangan yang cepat dan type-safe dari ujung ke ujung.

#### 2\. Arsitektur Tingkat Tinggi

Sistem akan dirancang sebagai serangkaian layanan yang saling terhubung, dikelola dalam satu monorepo dan di-deploy di Railway.

**Alur Data & Komunikasi:**

1.  **Pengguna** mengakses salah satu dari tiga aplikasi Next.js: `Landingpage`, `Dashboard` (aplikasi utama), atau `Admin Panel`.
2.  **Aplikasi Frontend** (Dashboard/Admin) berkomunikasi dengan backend melalui **tRPC**, memastikan API calls yang sepenuhnya type-safe.
3.  **Backend** (yang merupakan bagian dari Next.js API Routes) memproses logika bisnis.
      * Data transaksional (user, kontrak, workflow) disimpan di **PostgreSQL**.
      * Data cache, sesi, dan antrian tugas (background jobs) dikelola oleh **Redis**.
      * File dokumen (PDF, DOCX) diunggah dan disimpan dengan aman di **MinIO**.
4.  Saat kontrak diunggah atau diubah, sebuah tugas akan dikirim ke antrian **Redis**. Worker akan:
      * Mengindeks konten dokumen ke **OpenSearch** untuk pencarian.
      * Memanggil **Gemini API** untuk analisis, ringkasan, dan ekstraksi data.
5.  **Server WebSocket** yang terpisah menangani komunikasi real-time (notifikasi, kolaborasi live) antara backend dan klien yang terhubung.
6.  Integrasi eksternal (e-Meterai, Tanda Tangan Digital) dilakukan melalui API calls dari sisi backend.

#### 3\. Tumpukan Teknologi & Spesifikasi (Sesuai Permintaan)

| Komponen | Teknologi | Peran & Alasan |
| :--- | :--- | :--- |
| **Monorepo** | **Turborepo** | Mengelola workspace `apps` dan `packages` secara efisien, mempercepat build. |
| **Framework** | **Next.js 14+ (App Router)** | Backend dan frontend dalam satu framework. Digunakan untuk `dashboard`, `admin`, dan `landingpage`. |
| **API Layer** | **tRPC** | Membangun API yang 100% type-safe antara Next.js frontend dan backend tanpa perlu generate skema. |
| **UI/Styling** | **Tailwind CSS + shadcn/ui** | Desain yang cepat, modern, dan sangat dapat disesuaikan. `shadcn/ui` menyediakan komponen aksesibel. |
| **Database Utama** | **PostgreSQL** | Menyimpan data terstruktur: pengguna, metadata kontrak, alur kerja, audit log. |
| **Cache & Antrian** | **Redis** | Caching untuk performa, manajemen sesi WebSocket, dan sebagai message broker untuk background jobs. |
| **Pencarian** | **OpenSearch** | Untuk fitur pencarian *full-text search* yang canggih di seluruh konten dokumen. |
| **Real-time** | **Dedicated WebSocket Server** | Menangani notifikasi instan, status kolaborasi live (`@mentions`, `typing indicator`). |
| **AI & Analisis** | **Gemini API (Google)** | Untuk meringkas, menganalisis risiko, dan mengekstrak entitas dari dokumen kontrak. |
| **Penyimpanan File**| **MinIO (Open Source S3)** | Self-hosted object storage untuk menyimpan semua file kontrak, memberikan kontrol penuh atas data. |
| **Deployment** | **Railway.app** | Platform PaaS modern yang menyederhanakan proses deploy dan manajemen semua layanan (Next.js, Postgres, Redis, dll). |

#### 4\. Struktur Monorepo (Turborepo)

Struktur direktori akan diorganisir sebagai berikut untuk modularitas dan skalabilitas:

```
/
├── apps/
│   ├── dashboard/      # Aplikasi utama untuk pengguna (Next.js, tRPC, shadcn)
│   ├── admin/          # Panel admin terpisah (Next.js, tRPC, shadcn)
│   ├── landingpage/    # Halaman marketing & informasi (Next.js)
│   └── websocket/      # Server WebSocket dedicated (Node.js, ws/socket.io)
│
├── packages/
│   ├── ui/             # Komponen React bersama (shadcn/ui components)
│   ├── config/         # Konfigurasi bersama (ESLint, TypeScript)
│   ├── db/             # Skema & Klien Prisma/Drizzle untuk PostgreSQL
│   ├── api/            # Definisi router tRPC dan prosedur, digunakan oleh apps
│   └── lib/            # Fungsi utilitas bersama (mis. formating, auth logic)
│
└── package.json
```

#### 5\. Persyaratan Fungsional (Fitur Produk)

##### 5.1. Dashboard Pengguna (`apps/dashboard`)

  * **Manajemen Kontrak:**
      * **Unggah & Buat Kontrak:** Unggah DOCX/PDF atau buat dari template.
      * **Repositori Terpusat:** Tampilan daftar semua kontrak dengan status (Draft, Review, Signed), filter, dan sorting.
      * **Detail Kontrak:** Halaman tunggal untuk setiap kontrak yang menampilkan metadata, pratinjau dokumen, riwayat versi, dan jejak audit.
  * **Workflow & Kolaborasi:**
      * **Alur Kerja Persetujuan:** Kontrak secara otomatis bergerak melalui tahapan yang telah ditentukan (misal: Legal -\> Keuangan -\> Direksi).
      * **Komentar & @mentions:** Pengguna dapat meninggalkan komentar pada dokumen. Mentioning (`@username`) akan memicu notifikasi real-time via WebSocket.
      * **Riwayat Versi:** Setiap perubahan pada dokumen disimpan sebagai versi baru.
  * **Pencarian (Powered by OpenSearch):**
      * Pencarian metadata (nama kontrak, pihak, tanggal).
      * Pencarian teks lengkap di dalam isi semua dokumen yang diindeks.
  * **Fitur AI (Powered by Gemini API):**
      * **AI Summary:** Tombol "Ringkaskan Kontrak" untuk menghasilkan poin-poin kunci.
      * **AI Data Extraction:** Secara otomatis mengisi kolom metadata (nama pihak, tanggal efektif, nilai kontrak) dari teks.
      * **AI Risk Analysis:** Memberi highlight pada klausa yang tidak standar atau berpotensi berisiko (memerlukan *prompt engineering* yang cermat).
  * **Tindakan & Integrasi:**
      * Tombol "Minta Tanda Tangan" yang memulai proses penandatanganan via API PSrE.
      * Tombol "Bubuhkan e-Meterai" yang memanggil API PERURI.
  * **Notifikasi:** Lonceng notifikasi yang diperbarui secara real-time (via WebSocket) untuk semua aktivitas yang relevan.

##### 5.2. Panel Admin (`apps/admin`)

  * **Manajemen Pengguna:** Buat, edit, nonaktifkan pengguna. Tetapkan peran (Admin, Legal, User, dll).
  * **Manajemen Peran & Hak Akses (RBAC):** Konfigurasi hak akses untuk setiap peran.
  * **Workflow Builder:** Antarmuka visual (drag-and-drop jika memungkinkan) untuk membuat dan mengelola templat alur kerja persetujuan.
  * **Manajemen Template Kontrak:** Unggah dan kelola template dokumen yang dapat digunakan oleh pengguna.
  * **Jejak Audit Sistem (Audit Trail):** Log terpusat dari semua tindakan penting yang terjadi di platform.
  * **Monitoring & Analytics:** Dasbor sederhana untuk memantau penggunaan sistem, jumlah kontrak, dll.

##### 5.3. Halaman Depan (`apps/landingpage`)

  * Halaman marketing statis yang menjelaskan fitur, manfaat, dan harga produk.
  * Tombol Call-to-Action (CTA) untuk "Login" atau "Minta Demo".

#### 6\. Persyaratan Non-Fungsional

  * **Keamanan:**
      * Autentikasi menggunakan JWT.
      * Otorisasi di level API (tRPC middleware) dan database.
      * Semua data dienkripsi saat transit (HTTPS/SSL) dan saat disimpan (at-rest) di PostgreSQL dan MinIO.
      * Pencegahan serangan umum web (XSS, CSRF, SQL Injection).
  * **Performa:**
      * Waktu muat halaman interaktif (TTI) \< 3 detik.
      * Respon API (tRPC) rata-rata \< 200ms.
      * Respon pencarian OpenSearch \< 1 detik.
  * **Skalabilitas:** Arsitektur harus dapat diskalakan secara horizontal. Railway memudahkan penambahan sumber daya atau replika layanan.
  * **Kepatuhan (Compliance):**
      * Seluruh infrastruktur (Railway, MinIO) harus di-deploy di region data center Indonesia untuk mematuhi regulasi kedaulatan data (PP 71/2019).

#### 7\. Persyaratan Integrasi Pihak Ketiga

  * **Tanda Tangan Elektronik (Wajib):**
      * Integrasi API dengan setidaknya satu PSrE Indonesia yang diakui Kominfo (contoh: **PrivyID, VIDA Digital, Digisign**).
      * Proses harus mencakup pengiriman dokumen, penempatan kolom tanda tangan, dan penarikan kembali dokumen yang sudah ditandatangani beserta sertifikat digitalnya.
  * **Meterai Elektronik (Wajib):**
      * Integrasi API dengan **PERURI** atau distributor resminya untuk pembelian kuota, pembubuhan e-Meterai pada PDF, dan validasi.
  * **Layanan AI:**
      * Integrasi dengan **Google Gemini API** menggunakan SDK resmi. Perlu manajemen API Key yang aman.
  * **Layanan Notifikasi (Opsional):**
      * Integrasi dengan layanan email (misal: Resend, Postmark) untuk mengirim notifikasi email.