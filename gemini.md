This is a Contract Lifecycle Management (CLM) application named "ERMbeta". It's built on a modern tech stack using a monorepo architecture. The backend is powered by Next.js, tRPC, and Prisma, while the frontend uses Next.js with React Server Components and Tailwind CSS. Authentication is handled by Better Auth.

The project is structured as a monorepo using Turborepo, with the following packages:

-   `apps/admin`: Admin dashboard for managing users and system settings.
-   `apps/dashboard`: The main user-facing application for managing contracts.
-   `apps/landingpage`: The public landing page.
-   `apps/websocket`: WebSocket server for real-time collaboration.
-   `packages/api`: tRPC API definitions and routers.
-   `packages/auth`: Better Auth integration and components.
-   `packages/db`: Prisma schema, database client, and repositories.
-   `packages/lib`: Shared utility functions and constants.
-   `packages/testing`: Testing setup and mocks.
-   `packages/ui`: Shared UI components.

The development plan is outlined in `PLAN.md`. The project is currently 80% complete, with the remaining work focused on AI and search integration (Phase 7) and Indonesian compliance features (Phase 8).
