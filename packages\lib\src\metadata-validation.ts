import { z } from 'zod';

const metadataSchema = z.object({
  parties: z.array(z.object({ name: z.string(), address: z.string(), role: z.string() })),
  dates: z.object({ effectiveDate: z.string(), expirationDate: z.string() }),
  values: z.object({ contractValue: z.number(), currency: z.string() }),
  terms: z.object({ paymentTerms: z.string(), renewalTerms: z.string() }),
});

export function validateMetadata(metadata: unknown) {
  return metadataSchema.safeParse(metadata);
}
