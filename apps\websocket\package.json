{"name": "@clm/websocket", "version": "0.1.0", "private": true, "main": "src/server.ts", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@clm/auth": "*", "@clm/db": "*", "@clm/lib": "*", "cors": "^2.8.5", "express": "^4.19.2", "socket.io": "^4.7.5", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.14.10", "eslint": "^8.57.0", "tsx": "^4.16.2", "typescript": "^5.5.3"}}