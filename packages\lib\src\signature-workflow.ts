import { eSignatureProvider } from './e-signature';

export async function startSignatureProcess(document: <PERSON><PERSON><PERSON>, recipient: { name: string; email: string }) {
  const { documentId } = await eSignatureProvider.sendForSignature(document, recipient);

  let status: any = await eSignatureProvider.getSignatureStatus(documentId);
  while (status === 'pending') {
    await new Promise((resolve) => setTimeout(resolve, 5000));
    status = await eSignatureProvider.getSignatureStatus(documentId);
  }

  return status;
}
