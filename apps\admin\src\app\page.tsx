export default function AdminHome() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-8">
          CLM Admin Panel
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Contract Lifecycle Management Administration
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl">
          <div className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
            <h2 className="text-xl font-semibold mb-2">User Management</h2>
            <p className="text-gray-600">Manage users, roles, and permissions</p>
          </div>
          
          <div className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
            <h2 className="text-xl font-semibold mb-2">Workflow Builder</h2>
            <p className="text-gray-600">Create and manage approval workflows</p>
          </div>
          
          <div className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
            <h2 className="text-xl font-semibold mb-2">System Monitoring</h2>
            <p className="text-gray-600">Monitor system performance and usage</p>
          </div>
          
          <div className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
            <h2 className="text-xl font-semibold mb-2">Audit Trail</h2>
            <p className="text-gray-600">View system audit logs and activities</p>
          </div>
          
          <div className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
            <h2 className="text-xl font-semibold mb-2">Template Management</h2>
            <p className="text-gray-600">Manage contract templates</p>
          </div>
          
          <div className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
            <h2 className="text-xl font-semibold mb-2">Settings</h2>
            <p className="text-gray-600">Configure system settings</p>
          </div>
        </div>
      </div>
    </main>
  )
}
