'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAuth, signOut } from '@clm/auth'
import { Button } from '@clm/ui'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: '📊' },
  { name: '<PERSON><PERSON><PERSON>', href: '/dashboard/contracts', icon: '📄' },
  { name: 'Template', href: '/dashboard/templates', icon: '📝' },
  { name: 'Workflow', href: '/dashboard/workflows', icon: '🔄' },
  { name: 'Notifika<PERSON>', href: '/dashboard/notifications', icon: '🔔' },
  { name: 'Pengaturan', href: '/dashboard/settings', icon: '⚙️' },
]

export function Sidebar() {
  const pathname = usePathname()
  const { user } = useAuth()

  const handleLogout = async () => {
    await signOut()
  }

  return (
    <div className="flex flex-col w-64 bg-white border-r border-gray-200 h-full">
      {/* Logo */}
      <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-900">CLM Platform</h1>
      </div>

      {/* User Info */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
            {user?.name?.charAt(0).toUpperCase()}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user?.name}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {user?.role}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`
                flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                ${isActive
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }
              `}
            >
              <span className="mr-3">{item.icon}</span>
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* Logout */}
      <div className="p-4 border-t border-gray-200">
        <Button
          variant="outline"
          onClick={handleLogout}
          className="w-full"
        >
          Keluar
        </Button>
      </div>
    </div>
  )
}
