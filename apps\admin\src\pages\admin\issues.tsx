import { useState } from 'react'
import { But<PERSON>, Input, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@clm/ui'
import Link from 'next/link'

interface Issue {
  id: string
  title: string
  description: string
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'critical'
  category: 'bug' | 'feature_request' | 'support' | 'security' | 'performance'
  reportedBy: {
    id: string
    name: string
    email: string
  }
  assignedTo?: {
    id: string
    name: string
  }
  createdAt: string
  updatedAt: string
  comments: number
}

export default function IssuesManagementPage() {
  const [issues, setIssues] = useState<Issue[]>([
    {
      id: '1',
      title: 'Contract upload fails for large PDF files',
      description: 'Users are unable to upload PDF files larger than 10MB. The upload process hangs and eventually times out.',
      status: 'in_progress',
      priority: 'high',
      category: 'bug',
      reportedBy: {
        id: 'user1',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      assignedTo: {
        id: 'dev1',
        name: 'Developer Team',
      },
      createdAt: '2024-07-25T10:00:00Z',
      updatedAt: '2024-07-26T14:30:00Z',
      comments: 3,
    },
    {
      id: '2',
      title: 'Add bulk contract export feature',
      description: 'Request to add functionality to export multiple contracts at once in various formats (PDF, Excel, etc.)',
      status: 'open',
      priority: 'medium',
      category: 'feature_request',
      reportedBy: {
        id: 'user2',
        name: 'Jane Smith',
        email: '<EMAIL>',
      },
      createdAt: '2024-07-24T09:15:00Z',
      updatedAt: '2024-07-24T09:15:00Z',
      comments: 1,
    },
    {
      id: '3',
      title: 'Security vulnerability in file upload',
      description: 'Potential security issue with file upload validation. Need immediate review.',
      status: 'resolved',
      priority: 'critical',
      category: 'security',
      reportedBy: {
        id: 'security1',
        name: 'Security Team',
        email: '<EMAIL>',
      },
      assignedTo: {
        id: 'dev2',
        name: 'Security Team',
      },
      createdAt: '2024-07-20T16:45:00Z',
      updatedAt: '2024-07-22T11:20:00Z',
      comments: 8,
    },
  ])

  const [statusFilter, setStatusFilter] = useState<string>('')
  const [priorityFilter, setPriorityFilter] = useState<string>('')
  const [categoryFilter, setCategoryFilter] = useState<string>('')
  const [search, setSearch] = useState('')

  const filteredIssues = issues.filter(issue => {
    const matchesStatus = !statusFilter || issue.status === statusFilter
    const matchesPriority = !priorityFilter || issue.priority === priorityFilter
    const matchesCategory = !categoryFilter || issue.category === categoryFilter
    const matchesSearch = !search || 
      issue.title.toLowerCase().includes(search.toLowerCase()) ||
      issue.description.toLowerCase().includes(search.toLowerCase()) ||
      issue.reportedBy.name.toLowerCase().includes(search.toLowerCase())
    
    return matchesStatus && matchesPriority && matchesCategory && matchesSearch
  })

  const getStatusColor = (status: Issue['status']) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'resolved': return 'bg-green-100 text-green-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: Issue['priority']) => {
    switch (priority) {
      case 'low': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'critical': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getCategoryIcon = (category: Issue['category']) => {
    switch (category) {
      case 'bug': return '🐛'
      case 'feature_request': return '💡'
      case 'support': return '🆘'
      case 'security': return '🔒'
      case 'performance': return '⚡'
      default: return '📋'
    }
  }

  const updateIssueStatus = (id: string, newStatus: Issue['status']) => {
    setIssues(issues.map(issue => 
      issue.id === id 
        ? { ...issue, status: newStatus, updatedAt: new Date().toISOString() }
        : issue
    ))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link href="/admin" className="text-blue-600 hover:text-blue-800 text-sm">
                ← Back to Admin Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mt-2">Issues & Bug Reports</h1>
              <p className="text-gray-600">Manage user-reported issues and system problems</p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline">
                📊 Export Report
              </Button>
              <Button>
                🐛 Create Issue
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold text-blue-600">
                {issues.filter(i => i.status === 'open').length}
              </div>
              <p className="text-sm text-gray-500">Open Issues</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold text-yellow-600">
                {issues.filter(i => i.status === 'in_progress').length}
              </div>
              <p className="text-sm text-gray-500">In Progress</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold text-red-600">
                {issues.filter(i => i.priority === 'critical').length}
              </div>
              <p className="text-sm text-gray-500">Critical</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold text-green-600">
                {issues.filter(i => i.status === 'resolved').length}
              </div>
              <p className="text-sm text-gray-500">Resolved</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <Input
                placeholder="Search issues..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Status</option>
                <option value="open">Open</option>
                <option value="in_progress">In Progress</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
              </select>
              <select
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Priority</option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Categories</option>
                <option value="bug">Bug</option>
                <option value="feature_request">Feature Request</option>
                <option value="support">Support</option>
                <option value="security">Security</option>
                <option value="performance">Performance</option>
              </select>
              <Button variant="outline">
                🔄 Refresh
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Issues List */}
        <div className="space-y-4">
          {filteredIssues.length > 0 ? (
            filteredIssues.map((issue) => (
              <Card key={issue.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className="text-xl">{getCategoryIcon(issue.category)}</span>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {issue.title}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(issue.status)}`}>
                          {issue.status.replace('_', ' ').toUpperCase()}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(issue.priority)}`}>
                          {issue.priority.toUpperCase()}
                        </span>
                      </div>
                      
                      <p className="text-gray-700 mb-3 line-clamp-2">
                        {issue.description}
                      </p>
                      
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                        <span>
                          👤 Reported by {issue.reportedBy.name}
                        </span>
                        {issue.assignedTo && (
                          <span>
                            👨‍💻 Assigned to {issue.assignedTo.name}
                          </span>
                        )}
                        <span>
                          📅 {new Date(issue.createdAt).toLocaleDateString('id-ID')}
                        </span>
                        <span>
                          💬 {issue.comments} comments
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-2 ml-4">
                      <Button variant="outline" size="sm">
                        👁️ View Details
                      </Button>
                      {issue.status === 'open' && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => updateIssueStatus(issue.id, 'in_progress')}
                        >
                          🚀 Start Work
                        </Button>
                      )}
                      {issue.status === 'in_progress' && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => updateIssueStatus(issue.id, 'resolved')}
                        >
                          ✅ Mark Resolved
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        ✏️ Edit
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">🐛</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No issues found
                  </h3>
                  <p className="text-gray-500 mb-6">
                    {search || statusFilter || priorityFilter || categoryFilter
                      ? 'No issues match your current filters.'
                      : 'No issues have been reported yet.'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
