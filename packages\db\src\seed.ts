import { PrismaClient, UserRole } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create admin user with Better Auth account
  const adminPassword = await bcrypt.hash('admin123', 10)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin CLM',
      role: UserRole.ADMIN,
      emailVerified: true,
      accounts: {
        create: {
          accountId: '<EMAIL>',
          providerId: 'credential',
          password: adminPassword,
        },
      },
    },
  })

  // Create legal user with Better Auth account
  const legalPassword = await bcrypt.hash('legal123', 10)
  const legal = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Legal Team',
      role: UserRole.LEGAL,
      emailVerified: true,
      accounts: {
        create: {
          accountId: '<EMAIL>',
          providerId: 'credential',
          password: legalPassword,
        },
      },
    },
  })

  // Create regular user with Better Auth account
  const userPassword = await bcrypt.hash('user123', 10)
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Regular User',
      role: UserRole.USER,
      emailVerified: true,
      accounts: {
        create: {
          accountId: '<EMAIL>',
          providerId: 'credential',
          password: userPassword,
        },
      },
    },
  })

  // Create sample contract template
  const template = await prisma.template.create({
    data: {
      name: 'Standard Service Agreement',
      description: 'Template for standard service agreements',
      content: `
# SERVICE AGREEMENT

This Service Agreement ("Agreement") is entered into on [DATE] between:

**Service Provider:**
[PROVIDER_NAME]
[PROVIDER_ADDRESS]

**Client:**
[CLIENT_NAME]
[CLIENT_ADDRESS]

## 1. Services
The Service Provider agrees to provide the following services:
[SERVICE_DESCRIPTION]

## 2. Term
This Agreement shall commence on [START_DATE] and continue until [END_DATE].

## 3. Compensation
The Client agrees to pay [AMOUNT] [CURRENCY] for the services provided.

## 4. Termination
Either party may terminate this Agreement with [NOTICE_PERIOD] days written notice.

---
*This is a template document. Please customize according to your specific needs.*
      `,
      category: 'Service Agreement',
      createdBy: admin.id,
    },
  })

  console.log('✅ Database seeded successfully!')
  console.log(`👤 Admin user: <EMAIL> / admin123`)
  console.log(`⚖️ Legal user: <EMAIL> / legal123`)
  console.log(`👥 Regular user: <EMAIL> / user123`)
  console.log(`📄 Template created: ${template.name}`)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
