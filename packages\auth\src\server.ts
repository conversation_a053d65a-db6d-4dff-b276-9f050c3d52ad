import { auth } from './config';
import type { BetterAuth } from 'better-auth';

const authServer = auth;
import { headers } from "next/headers"
import { redirect } from "next/navigation"

// Server-side authentication utilities
export async function getServerSession() {
  try {
    const session = await auth.getSession({
      headers: await headers(),
    })
    return session
  } catch (error) {
    console.error("Failed to get server session:", error)
    return null
  }
}

export async function requireServerAuth() {
  const session = await getServerSession()
  if (!session?.user) {
    redirect("/login")
  }
  return session
}

export async function requireServerRole(role: string) {
  const session = await requireServerAuth()
  if (session.user.role !== role) {
    redirect("/unauthorized")
  }
  return session
}

export async function requireServerAnyRole(roles: string[]) {
  const session = await requireServerAuth()
  if (!roles.includes(session.user.role || "")) {
    redirect("/unauthorized")
  }
  return session
}

// Server actions for authentication
export async function signInAction(email: string, password: string) {
  try {
    const result = await auth.signInEmail({
      body: {
        email,
        password,
      },
    })
    
    if (result.error) {
      return { error: result.error.message }
    }
    
    return { success: true, user: result.data?.user }
  } catch (error) {
    console.error("Sign in error:", error)
    return { error: "Sign in failed" }
  }
}

export async function signUpAction(email: string, password: string, name: string) {
  try {
    const result = await auth.signUpEmail({
      body: {
        email,
        password,
        name,
      },
    })
    
    if (result.error) {
      return { error: result.error.message }
    }
    
    return { success: true, user: result.data?.user }
  } catch (error) {
    console.error("Sign up error:", error)
    return { error: "Sign up failed" }
  }
}

export async function signOutAction() {
  try {
    await auth.api.signOut({
      headers: await headers(),
    })
    return { success: true }
  } catch (error) {
    console.error("Sign out error:", error)
    return { error: "Sign out failed" }
  }
}

export async function updateUserAction(data: {
  name?: string
  avatar?: string
}) {
  try {
    const session = await requireServerAuth()
    
    const result = await auth.api.updateUser({
      body: data,
      headers: await headers(),
    })
    
    if (result.error) {
      return { error: result.error.message }
    }
    
    return { success: true, user: result.data }
  } catch (error) {
    console.error("Update user error:", error)
    return { error: "Update failed" }
  }
}

export async function changePasswordAction(
  currentPassword: string,
  newPassword: string
) {
  try {
    const session = await requireServerAuth()
    
    const result = await auth.api.changePassword({
      body: {
        currentPassword,
        newPassword,
      },
      headers: await headers(),
    })
    
    if (result.error) {
      return { error: result.error.message }
    }
    
    return { success: true }
  } catch (error) {
    console.error("Change password error:", error)
    return { error: "Password change failed" }
  }
}

// Admin server actions
export async function listUsersAction(options?: {
  limit?: number
  offset?: number
  search?: string
}) {
  try {
    await requireServerRole("ADMIN")
    
    const result = await auth.api.listUsers({
      query: {
        limit: options?.limit?.toString() || "10",
        offset: options?.offset?.toString() || "0",
        search: options?.search,
      },
      headers: await headers(),
    })
    
    if (result.error) {
      return { error: result.error.message }
    }
    
    return { success: true, users: result.data }
  } catch (error) {
    console.error("List users error:", error)
    return { error: "Failed to list users" }
  }
}

export async function updateUserRoleAction(userId: string, role: string) {
  try {
    await requireServerRole("ADMIN")
    
    // Use Prisma directly for role updates since Better Auth might not expose this
    const { prisma } = await import("@clm/db")
    
    const user = await prisma.user.update({
      where: { id: userId },
      data: { role: role as any },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
      },
    })
    
    return { success: true, user }
  } catch (error) {
    console.error("Update user role error:", error)
    return { error: "Failed to update user role" }
  }
}

export async function deactivateUserAction(userId: string) {
  try {
    await requireServerRole("ADMIN")
    
    const { prisma } = await import("@clm/db")
    
    const user = await prisma.user.update({
      where: { id: userId },
      data: { isActive: false },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
      },
    })
    
    return { success: true, user }
  } catch (error) {
    console.error("Deactivate user error:", error)
    return { error: "Failed to deactivate user" }
  }
}

// Organization server actions
export async function createOrganizationAction(name: string, description?: string) {
  try {
    await requireServerRole("ADMIN")
    
    const result = await auth.api.createOrganization({
      body: {
        name,
      },
      headers: await headers(),
    })
    
    if (result.error) {
      return { error: result.error.message }
    }
    
    return { success: true, organization: result.data }
  } catch (error) {
    console.error("Create organization error:", error)
    return { error: "Failed to create organization" }
  }
}


