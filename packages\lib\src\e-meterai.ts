export interface EMeteraiProvider {
  applyMeterai(document: Buffer): Promise<{ success: boolean; documentId: string }>;
  validateMeterai(documentId: string): Promise<boolean>;
}

class MockEMeteraiProvider implements EMeteraiProvider {
  async applyMeterai(document: Buffer) {
    console.log('Applying e-Meterai to document');
    return { success: true, documentId: `meterai-${Date.now()}` };
  }

  async validateMeterai(documentId: string) {
    return true;
  }
}

export const eMeteraiProvider = new MockEMeteraiProvider();
