import { z } from 'zod'

// Common validation schemas
export const emailSchema = z.string().email('Email tidak valid')

export const passwordSchema = z
  .string()
  .min(8, 'Password minimal 8 karakter')
  .regex(/[A-Z]/, 'Password harus mengandung huruf besar')
  .regex(/[a-z]/, 'Password harus mengandung huruf kecil')
  .regex(/[0-9]/, 'Password harus mengandung angka')

export const phoneSchema = z
  .string()
  .regex(/^(\+62|62|0)[0-9]{9,13}$/, 'Nomor telepon tidak valid')

export const contractTitleSchema = z
  .string()
  .min(1, 'Judul kontrak wajib diisi')
  .max(200, 'Judul kontrak maksimal 200 karakter')

export const contractValueSchema = z
  .number()
  .positive('Nilai kontrak harus positif')
  .max(999999999999, '<PERSON><PERSON> kontrak terlalu besar')

// File validation
export const fileSchema = z.object({
  name: z.string(),
  size: z.number().max(10 * 1024 * 1024, 'File maksimal 10MB'), // 10MB
  type: z.string().refine(
    (type) => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(type),
    'Hanya file PDF dan Word yang diizinkan'
  ),
})

// Indonesian specific validations
export const nikSchema = z
  .string()
  .length(16, 'NIK harus 16 digit')
  .regex(/^[0-9]+$/, 'NIK hanya boleh berisi angka')

export const npwpSchema = z
  .string()
  .regex(/^[0-9]{2}\.[0-9]{3}\.[0-9]{3}\.[0-9]{1}-[0-9]{3}\.[0-9]{3}$/, 'Format NPWP tidak valid')

// Business validation helpers
export function validateContractDates(startDate: Date, endDate: Date): boolean {
  return endDate > startDate
}

export function validateBusinessEmail(email: string): boolean {
  const businessDomains = ['.co.id', '.com', '.org', '.net', '.gov.id']
  return businessDomains.some(domain => email.toLowerCase().includes(domain))
}

export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .toLowerCase()
}
