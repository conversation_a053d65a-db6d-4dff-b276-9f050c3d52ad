import express from 'express'
import { createServer } from 'http'
import { Server } from 'socket.io'
import cors from 'cors'
import { auth } from '@clm/auth'
import { sessionStore } from '@clm/lib'
import { ContractCollaborationHandler } from './handlers/contract-collaboration'
import { NotificationHandler } from './handlers/notifications'
import { PresenceHandler } from './handlers/presence'
import { CommentHandler } from './handlers/comments'

const app = express()
const server = createServer(app)

// CORS configuration
const corsOptions = {
  origin: process.env.WEBSOCKET_CORS_ORIGIN?.split(',') || [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
  ],
  credentials: true,
}

app.use(cors(corsOptions))

// Socket.IO setup
const io = new Server(server, {
  cors: corsOptions,
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
})

// Authentication middleware for Socket.IO
io.use(async (socket, next) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '')
    
    if (!token) {
      return next(new Error('Authentication token required'))
    }

    // Verify token with Better Auth
    const session = await auth.api.getSession({
      headers: {
        authorization: `Bearer ${token}`,
      },
    })

    if (!session?.user) {
      return next(new Error('Invalid authentication token'))
    }

    // Check if user is active
    if (!session.user.isActive) {
      return next(new Error('Account is deactivated'))
    }

    // Store user info in socket
    socket.data.user = session.user
    socket.data.session = session.session

    // Create WebSocket session
    const wsSession = sessionStore.createWebSocketSession(
      socket.id,
      {
        userId: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: session.user.role || 'USER',
        avatar: session.user.avatar,
        createdAt: Date.now(),
        lastActivity: Date.now(),
      }
    )

    socket.data.wsSession = wsSession

    next()
  } catch (error) {
    console.error('Socket authentication error:', error)
    next(new Error('Authentication failed'))
  }
})

// Initialize handlers
const contractCollaboration = new ContractCollaborationHandler(io)
const notifications = new NotificationHandler(io)
const presence = new PresenceHandler(io)
const comments = new CommentHandler(io)

// Connection handling
io.on('connection', (socket) => {
  const user = socket.data.user
  const wsSession = socket.data.wsSession

  console.log(`User ${user.name} (${user.id}) connected via WebSocket`)

  // Handle presence
  presence.handleConnection(socket)

  // Handle contract collaboration
  contractCollaboration.handleConnection(socket)

  // Handle notifications
  notifications.handleConnection(socket)

  // Handle comments
  comments.handleConnection(socket)

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    console.log(`User ${user.name} (${user.id}) disconnected: ${reason}`)
    
    // Clean up presence
    presence.handleDisconnection(socket)
    
    // Clean up contract collaboration
    contractCollaboration.handleDisconnection(socket)
    
    // Remove WebSocket session
    sessionStore.deleteWebSocketSession(socket.id)
  })

  // Handle errors
  socket.on('error', (error) => {
    console.error(`Socket error for user ${user.id}:`, error)
  })

  // Heartbeat to keep connection alive
  socket.on('ping', () => {
    socket.emit('pong')
    
    // Update last activity
    sessionStore.updateWebSocketSession(socket.id, {
      lastActivity: Date.now(),
    })
  })
})

// Health check endpoint
app.get('/health', (req, res) => {
  const stats = sessionStore.getStats()
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    connections: stats.totalWebSocketConnections,
    uptime: process.uptime(),
  })
})

// Stats endpoint
app.get('/stats', (req, res) => {
  const stats = sessionStore.getStats()
  res.json({
    ...stats,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
  })
})

// Start server
const PORT = process.env.WEBSOCKET_PORT || 3003

server.listen(PORT, () => {
  console.log(`🚀 WebSocket server running on port ${PORT}`)
  console.log(`📡 CORS origins: ${corsOptions.origin.join(', ')}`)
})

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully')
  server.close(() => {
    console.log('WebSocket server closed')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully')
  server.close(() => {
    console.log('WebSocket server closed')
    process.exit(0)
  })
})

export { io }
