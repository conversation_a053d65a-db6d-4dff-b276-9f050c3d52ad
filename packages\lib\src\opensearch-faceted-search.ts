import { opensearchClient } from './opensearch';

export async function facetedSearch(query: string, facets: Record<string, string>) {
  const response = await opensearchClient.search({
    index: 'contracts',
    body: {
      query: {
        bool: {
          must: {
            multi_match: {
              query,
              fields: ['attachment.content', 'title', 'description'],
              fuzziness: 'AUTO',
            },
          },
          filter: Object.entries(facets).map(([field, value]) => ({ term: { [field]: value } })),
        },
      },
      aggs: {
        ...Object.keys(facets).reduce((acc, field) => ({ ...acc, [field]: { terms: { field } } }), {}),
      },
    },
  });

  return {
    hits: response.body.hits.hits,
    aggregations: response.body.aggregations,
  };
}
