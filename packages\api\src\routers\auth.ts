import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, publicProcedure, protectedProcedure, adminProcedure } from '../trpc'
import { AuthRepository } from '@clm/db'

const authRepo = new AuthRepository()

export const authRouter = createTRPCRouter({
  me: protectedProcedure.query(({ ctx }) => {
    return ctx.user
  }),

  updateProfile: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1).optional(),
        avatar: z.string().url().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return authRepo.updateUser(ctx.user.id, input)
    }),

  changePassword: protectedProcedure
    .input(
      z.object({
        currentPassword: z.string().min(1),
        newPassword: z.string().min(6),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const user = await authRepo.findUserById(ctx.user.id)
      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      const userWithPassword = await authRepo.findUserByEmail(user.email)
      if (!userWithPassword?.accounts[0]?.password) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Password not found',
        })
      }

      const isValidPassword = await authRepo.verifyPassword(
        input.currentPassword,
        userWithPassword.accounts[0].password
      )

      if (!isValidPassword) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Current password is incorrect',
        })
      }

      return authRepo.updatePassword(ctx.user.id, input.newPassword)
    }),

  getSessions: protectedProcedure.query(({ ctx }) => {
    return authRepo.getUserSessions(ctx.user.id)
  }),

  revokeSession: protectedProcedure
    .input(z.object({ sessionId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Only allow users to revoke their own sessions
      const sessions = await authRepo.getUserSessions(ctx.user.id)
      const session = sessions.find((s: any) => s.id === input.sessionId)

      if (!session) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Session not found',
        })
      }

      return authRepo.deleteSession(session.token)
    }),

  // Admin-only endpoints
  getAllUsers: adminProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(10),
        offset: z.number().min(0).default(0),
        search: z.string().optional(),
        role: z.enum(['ADMIN', 'LEGAL', 'FINANCE', 'USER', 'VIEWER']).optional(),
        isActive: z.boolean().optional(),
      })
    )
    .query(({ input }) => {
      return authRepo.getAllUsers(input)
    }),

  getUserById: adminProcedure
    .input(z.object({ userId: z.string() }))
    .query(({ input }) => {
      return authRepo.findUserById(input.userId)
    }),

  createUser: adminProcedure
    .input(
      z.object({
        email: z.string().email(),
        name: z.string().min(1),
        password: z.string().min(6),
        role: z.enum(['ADMIN', 'LEGAL', 'FINANCE', 'USER', 'VIEWER']).default('USER'),
      })
    )
    .mutation(async ({ input }) => {
      // Check if user already exists
      const existingUser = await authRepo.findUserByEmail(input.email)
      if (existingUser) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'User with this email already exists',
        })
      }

      return authRepo.createUser(input)
    }),

  updateUser: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        name: z.string().min(1).optional(),
        email: z.string().email().optional(),
        role: z.enum(['ADMIN', 'LEGAL', 'FINANCE', 'USER', 'VIEWER']).optional(),
        isActive: z.boolean().optional(),
        emailVerified: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { userId, ...updateData } = input

      // Prevent users from updating their own role
      if (userId === ctx.user.id && updateData.role) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Cannot update your own role',
        })
      }

      // If updating email, check for duplicates
      if (updateData.email) {
        const existingUser = await authRepo.findUserByEmail(updateData.email)
        if (existingUser && existingUser.id !== userId) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'User with this email already exists',
          })
        }
      }

      return authRepo.updateUser(userId, updateData)
    }),

  deactivateUser: adminProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Prevent users from deactivating themselves
      if (input.userId === ctx.user.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Cannot deactivate your own account',
        })
      }

      return authRepo.deactivateUser(input.userId)
    }),

  activateUser: adminProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(({ input }) => {
      return authRepo.activateUser(input.userId)
    }),

  deleteUser: adminProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Prevent users from deleting themselves
      if (input.userId === ctx.user.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Cannot delete your own account',
        })
      }

      return authRepo.deleteUser(input.userId)
    }),

  promoteToAdmin: adminProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(({ ctx, input }) => {
      return authRepo.promoteToAdmin(input.userId, ctx.user.id)
    }),

  demoteFromAdmin: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        newRole: z.enum(['LEGAL', 'FINANCE', 'USER', 'VIEWER']).default('USER'),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Prevent users from demoting themselves
      if (input.userId === ctx.user.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Cannot demote your own account',
        })
      }

      return authRepo.demoteFromAdmin(input.userId, ctx.user.id, input.newRole)
    }),

  getUserStats: adminProcedure.query(() => {
    return authRepo.getUserStats()
  }),

  getAdminUsers: adminProcedure.query(() => {
    return authRepo.getAdminUsers()
  }),

  resetUserPassword: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        newPassword: z.string().min(6),
      })
    )
    .mutation(({ input }) => {
      return authRepo.updatePassword(input.userId, input.newPassword)
    }),

  revokeUserSessions: adminProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(({ input }) => {
      return authRepo.deleteUserSessions(input.userId)
    }),

  cleanupExpiredSessions: adminProcedure.mutation(() => {
    return authRepo.cleanupExpiredSessions()
  }),
})
