import { describe, it, expect } from 'vitest'
import {
  emailSchema,
  passwordSchema,
  phoneSchema,
  contractTitleSchema,
  contractValueSchema,
  fileSchema,
  nikSchema,
  npwpSchema,
  validateContractDates,
  validateBusinessEmail,
  sanitizeFileName,
} from '@clm/lib'

describe('Validation Utilities', () => {
  describe('emailSchema', () => {
    it('should validate correct email formats', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ]

      validEmails.forEach(email => {
        expect(emailSchema.safeParse(email).success).toBe(true)
      })
    })

    it('should reject invalid email formats', () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        '<EMAIL>',
        'user@domain',
        '',
        'user <EMAIL>',
        '<EMAIL>',
      ]

      invalidEmails.forEach(email => {
        expect(emailSchema.safeParse(email).success).toBe(false)
      })
    })
  })

  describe('nikSchema', () => {
    it('should validate correct NIK format', () => {
      const validNIKs = [
        '3201234567890123', // 16 digits
        '1234567890123456',
        '9876543210987654',
      ]

      validNIKs.forEach(nik => {
        expect(nikSchema.safeParse(nik).success).toBe(true)
      })
    })

    it('should reject invalid NIK formats', () => {
      const invalidNIKs = [
        '123456789012345', // 15 digits
        '12345678901234567', // 17 digits
        '320123456789012a', // contains letter
        '3201-2345-6789-0123', // contains dashes
        '',
        '0000000000000000', // all zeros (valid format but questionable)
      ]

      invalidNIKs.forEach(nik => {
        expect(nikSchema.safeParse(nik).success).toBe(false)
      })
    })
  })

  describe('npwpSchema', () => {
    it('should validate correct NPWP format', () => {
      const validNPWPs = [
        '12.345.678.9-012.345', // Standard format
        '98.765.432.1-098.765',
        '11.222.333.4-555.666',
      ]

      validNPWPs.forEach(npwp => {
        expect(npwpSchema.safeParse(npwp).success).toBe(true)
      })
    })

    it('should reject invalid NPWP formats', () => {
      const invalidNPWPs = [
        '12.345.678.9-012.34', // Missing digit
        '12.345.678.9-012.3456', // Extra digit
        '12.345.678.a-012.345', // Contains letter
        '1234567890123456', // Wrong format
        '12345678901234', // Wrong format
        '',
        '123456789012345', // No formatting
      ]

      invalidNPWPs.forEach(npwp => {
        expect(npwpSchema.safeParse(npwp).success).toBe(false)
      })
    })
  })

  describe('phoneSchema', () => {
    it('should validate Indonesian phone numbers', () => {
      const validPhones = [
        '+6281234567890', // International format
        '081234567890', // Local format
        '6281234567890', // Without plus
        '08123456789', // Shorter mobile
      ]

      validPhones.forEach(phone => {
        expect(phoneSchema.safeParse(phone).success).toBe(true)
      })
    })

    it('should reject invalid phone numbers', () => {
      const invalidPhones = [
        '123456', // Too short
        '+1234567890123456789', // Too long
        'abc123456789', // Contains letters
        '', // Empty
        '+62', // Only country code
        '0812345', // Too short for mobile
        '021-12345678', // Landline format not supported
      ]

      invalidPhones.forEach(phone => {
        expect(phoneSchema.safeParse(phone).success).toBe(false)
      })
    })
  })

  describe('passwordSchema', () => {
    it('should validate strong passwords', () => {
      const validPasswords = [
        'Password123', // Mixed case, number
        'MyStr0ngPass', // Mixed case, number
        'Complex1Password', // Long with requirements
        'Test123A', // Minimum requirements
      ]

      validPasswords.forEach(password => {
        expect(passwordSchema.safeParse(password).success).toBe(true)
      })
    })

    it('should reject weak passwords', () => {
      const invalidPasswords = [
        'password', // No uppercase, number
        'PASSWORD', // No lowercase, number
        'Password', // No number
        'Pass123', // Too short
        '12345678', // Only numbers
        '', // Empty
        'pass123', // No uppercase
      ]

      invalidPasswords.forEach(password => {
        expect(passwordSchema.safeParse(password).success).toBe(false)
      })
    })
  })

  describe('contractValueSchema', () => {
    it('should validate positive contract values', () => {
      const validValues = [
        1000000, // 1 million
        50000.50, // With decimals
        0.01, // Small amount
        999999999999, // Large amount
      ]

      validValues.forEach(value => {
        expect(contractValueSchema.safeParse(value).success).toBe(true)
      })
    })

    it('should reject invalid contract values', () => {
      const invalidValues = [
        -1000, // Negative
        0, // Zero
        -0.01, // Negative decimal
        1000000000000, // Too large
      ]

      invalidValues.forEach(value => {
        expect(contractValueSchema.safeParse(value).success).toBe(false)
      })
    })
  })

  describe('fileSchema', () => {
    it('should validate allowed file types and sizes', () => {
      const validFiles = [
        { name: 'contract.pdf', size: 1024 * 1024, type: 'application/pdf' },
        { name: 'document.docx', size: 2 * 1024 * 1024, type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
        { name: 'old-doc.doc', size: 3 * 1024 * 1024, type: 'application/msword' },
      ]

      validFiles.forEach(file => {
        expect(fileSchema.safeParse(file).success).toBe(true)
      })
    })

    it('should reject invalid files', () => {
      const invalidFiles = [
        { name: 'script.js', size: 1024, type: 'application/javascript' }, // Wrong type
        { name: 'large.pdf', size: 15 * 1024 * 1024, type: 'application/pdf' }, // Too large
        { name: 'image.jpg', size: 1024, type: 'image/jpeg' }, // Wrong type
      ]

      invalidFiles.forEach(file => {
        expect(fileSchema.safeParse(file).success).toBe(false)
      })
    })
  })

  describe('validateContractDates', () => {
    it('should validate correct date ranges', () => {
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-12-31')

      expect(validateContractDates(startDate, endDate)).toBe(true)
    })

    it('should reject invalid date ranges', () => {
      const startDate = new Date('2024-12-31')
      const endDate = new Date('2024-01-01')

      expect(validateContractDates(startDate, endDate)).toBe(false)
    })

    it('should reject same dates', () => {
      const date = new Date('2024-01-01')

      expect(validateContractDates(date, date)).toBe(false)
    })
  })

  describe('validateBusinessEmail', () => {
    it('should validate business email domains', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ]

      validEmails.forEach(email => {
        expect(validateBusinessEmail(email)).toBe(true)
      })
    })

    it('should reject non-business domains', () => {
      const invalidEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ]

      invalidEmails.forEach(email => {
        expect(validateBusinessEmail(email)).toBe(false)
      })
    })
  })

  describe('sanitizeFileName', () => {
    it('should sanitize file names correctly', () => {
      expect(sanitizeFileName('Contract Document.pdf')).toBe('contract_document.pdf')
      expect(sanitizeFileName('File@#$%Name.docx')).toBe('file_name.docx')
      expect(sanitizeFileName('Multiple___Underscores.txt')).toBe('multiple_underscores.txt')
      expect(sanitizeFileName('UPPERCASE.PDF')).toBe('uppercase.pdf')
    })

    it('should handle special characters', () => {
      expect(sanitizeFileName('file with spaces.pdf')).toBe('file_with_spaces.pdf')
      expect(sanitizeFileName('file-with-dashes.pdf')).toBe('file-with-dashes.pdf')
      expect(sanitizeFileName('file.with.dots.pdf')).toBe('file.with.dots.pdf')
    })
  })
})
