import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createMockWebSocket, mockUsers } from '../utils/test-helpers'

// Mock Socket.io client
const mockSocket = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  connect: vi.fn(),
  disconnect: vi.fn(),
  connected: true,
  id: 'test-socket-id',
}

vi.mock('socket.io-client', () => ({
  io: vi.fn(() => mockSocket),
}))

describe('WebSocket Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    mockSocket.connected = true
  })

  describe('Connection Management', () => {
    it('should establish WebSocket connection with authentication', () => {
      const mockIo = vi.fn(() => mockSocket)
      
      // Simulate connection with auth token
      const socket = mockIo('ws://localhost:3003', {
        auth: {
          token: 'test-auth-token',
        },
      })

      expect(mockIo).toHaveBeenCalledWith('ws://localhost:3003', {
        auth: {
          token: 'test-auth-token',
        },
      })
      expect(socket).toBe(mockSocket)
    })

    it('should handle connection errors', () => {
      const errorHandler = vi.fn()
      mockSocket.on('connect_error', errorHandler)

      // Simulate connection error
      const error = new Error('Authentication failed')
      mockSocket.on.mock.calls.find(call => call[0] === 'connect_error')?.[1](error)

      expect(errorHandler).toHaveBeenCalledWith(error)
    })

    it('should handle disconnection', () => {
      const disconnectHandler = vi.fn()
      mockSocket.on('disconnect', disconnectHandler)

      // Simulate disconnection
      mockSocket.connected = false
      mockSocket.on.mock.calls.find(call => call[0] === 'disconnect')?.[1]('transport close')

      expect(disconnectHandler).toHaveBeenCalledWith('transport close')
    })
  })

  describe('Contract Collaboration', () => {
    it('should join contract room successfully', () => {
      const joinHandler = vi.fn()
      mockSocket.on('contract:joined', joinHandler)

      // Emit join contract event
      mockSocket.emit('contract:join', { contractId: 'test-contract-1' })

      expect(mockSocket.emit).toHaveBeenCalledWith('contract:join', {
        contractId: 'test-contract-1',
      })
    })

    it('should handle user joining contract room', () => {
      const userJoinedHandler = vi.fn()
      mockSocket.on('contract:user-joined', userJoinedHandler)

      // Simulate user joining
      const joinData = {
        userId: mockUsers.legal.id,
        userName: mockUsers.legal.name,
        avatar: mockUsers.legal.avatar,
        timestamp: Date.now(),
      }

      mockSocket.on.mock.calls.find(call => call[0] === 'contract:user-joined')?.[1](joinData)

      expect(userJoinedHandler).toHaveBeenCalledWith(joinData)
    })

    it('should handle cursor position updates', () => {
      const cursorUpdateHandler = vi.fn()
      mockSocket.on('contract:cursor-update', cursorUpdateHandler)

      // Emit cursor update
      const cursorData = {
        contractId: 'test-contract-1',
        position: 150,
      }
      mockSocket.emit('contract:cursor', cursorData)

      expect(mockSocket.emit).toHaveBeenCalledWith('contract:cursor', cursorData)
    })

    it('should handle text selection updates', () => {
      const selectionUpdateHandler = vi.fn()
      mockSocket.on('contract:selection-update', selectionUpdateHandler)

      // Emit selection update
      const selectionData = {
        contractId: 'test-contract-1',
        start: 100,
        end: 200,
      }
      mockSocket.emit('contract:selection', selectionData)

      expect(mockSocket.emit).toHaveBeenCalledWith('contract:selection', selectionData)
    })

    it('should handle text changes with operational transformation', () => {
      const textChangeHandler = vi.fn()
      mockSocket.on('contract:text-changed', textChangeHandler)

      // Emit text change
      const textChangeData = {
        contractId: 'test-contract-1',
        operation: {
          type: 'insert' as const,
          position: 100,
          content: 'New text content',
        },
        version: 1,
      }
      mockSocket.emit('contract:text-change', textChangeData)

      expect(mockSocket.emit).toHaveBeenCalledWith('contract:text-change', textChangeData)
    })

    it('should handle typing indicators', () => {
      const typingStartHandler = vi.fn()
      const typingStopHandler = vi.fn()
      
      mockSocket.on('contract:typing-start', typingStartHandler)
      mockSocket.on('contract:typing-stop', typingStopHandler)

      // Emit typing start
      mockSocket.emit('contract:typing-start', { contractId: 'test-contract-1' })
      expect(mockSocket.emit).toHaveBeenCalledWith('contract:typing-start', {
        contractId: 'test-contract-1',
      })

      // Emit typing stop
      mockSocket.emit('contract:typing-stop', { contractId: 'test-contract-1' })
      expect(mockSocket.emit).toHaveBeenCalledWith('contract:typing-stop', {
        contractId: 'test-contract-1',
      })
    })
  })

  describe('Real-time Notifications', () => {
    it('should receive new notifications', () => {
      const notificationHandler = vi.fn()
      mockSocket.on('notification:new', notificationHandler)

      // Simulate receiving notification
      const notification = {
        id: 'notification-1',
        title: 'Kontrak Baru Dibuat',
        message: 'Kontrak "Service Agreement Q1 2024" telah dibuat',
        type: 'CONTRACT_CREATED',
        contractId: 'test-contract-1',
        isRead: false,
        createdAt: new Date().toISOString(),
      }

      mockSocket.on.mock.calls.find(call => call[0] === 'notification:new')?.[1](notification)

      expect(notificationHandler).toHaveBeenCalledWith(notification)
    })

    it('should handle notification mark as read', () => {
      const markReadHandler = vi.fn()
      mockSocket.on('notification:marked-read', markReadHandler)

      // Emit mark as read
      mockSocket.emit('notification:mark-read', { notificationId: 'notification-1' })

      expect(mockSocket.emit).toHaveBeenCalledWith('notification:mark-read', {
        notificationId: 'notification-1',
      })
    })

    it('should receive unread count updates', () => {
      const unreadCountHandler = vi.fn()
      mockSocket.on('notification:unread-count', unreadCountHandler)

      // Simulate unread count update
      const countData = { count: 5 }
      mockSocket.on.mock.calls.find(call => call[0] === 'notification:unread-count')?.[1](countData)

      expect(unreadCountHandler).toHaveBeenCalledWith(countData)
    })
  })

  describe('User Presence', () => {
    it('should update user status', () => {
      const statusUpdateHandler = vi.fn()
      mockSocket.on('presence:user-update', statusUpdateHandler)

      // Emit status update
      mockSocket.emit('presence:status', { status: 'away' })

      expect(mockSocket.emit).toHaveBeenCalledWith('presence:status', {
        status: 'away',
      })
    })

    it('should handle page/location updates', () => {
      // Emit page update
      mockSocket.emit('presence:page', { page: '/dashboard/contracts/123' })

      expect(mockSocket.emit).toHaveBeenCalledWith('presence:page', {
        page: '/dashboard/contracts/123',
      })
    })

    it('should receive online users list', () => {
      const onlineUsersHandler = vi.fn()
      mockSocket.on('presence:online-users', onlineUsersHandler)

      // Request online users
      mockSocket.emit('presence:get-online')

      expect(mockSocket.emit).toHaveBeenCalledWith('presence:get-online')
    })

    it('should handle heartbeat', () => {
      // Emit heartbeat
      mockSocket.emit('presence:heartbeat')

      expect(mockSocket.emit).toHaveBeenCalledWith('presence:heartbeat')
    })
  })

  describe('Comments System', () => {
    it('should add comment to contract', () => {
      const commentAddedHandler = vi.fn()
      mockSocket.on('comment:added', commentAddedHandler)

      // Emit add comment
      const commentData = {
        contractId: 'test-contract-1',
        content: 'This is a test comment',
        parentId: null,
      }
      mockSocket.emit('comment:add', commentData)

      expect(mockSocket.emit).toHaveBeenCalledWith('comment:add', commentData)
    })

    it('should handle comment updates', () => {
      const commentUpdatedHandler = vi.fn()
      mockSocket.on('comment:updated', commentUpdatedHandler)

      // Emit update comment
      const updateData = {
        commentId: 'comment-1',
        content: 'Updated comment content',
      }
      mockSocket.emit('comment:update', updateData)

      expect(mockSocket.emit).toHaveBeenCalledWith('comment:update', updateData)
    })

    it('should handle comment deletion', () => {
      const commentDeletedHandler = vi.fn()
      mockSocket.on('comment:deleted', commentDeletedHandler)

      // Emit delete comment
      mockSocket.emit('comment:delete', { commentId: 'comment-1' })

      expect(mockSocket.emit).toHaveBeenCalledWith('comment:delete', {
        commentId: 'comment-1',
      })
    })

    it('should join and leave comment rooms', () => {
      // Join contract comments room
      mockSocket.emit('comment:join-contract', { contractId: 'test-contract-1' })
      expect(mockSocket.emit).toHaveBeenCalledWith('comment:join-contract', {
        contractId: 'test-contract-1',
      })

      // Leave contract comments room
      mockSocket.emit('comment:leave-contract', { contractId: 'test-contract-1' })
      expect(mockSocket.emit).toHaveBeenCalledWith('comment:leave-contract', {
        contractId: 'test-contract-1',
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle WebSocket errors', () => {
      const errorHandler = vi.fn()
      mockSocket.on('error', errorHandler)

      // Simulate error
      const error = { message: 'Invalid contract data' }
      mockSocket.on.mock.calls.find(call => call[0] === 'error')?.[1](error)

      expect(errorHandler).toHaveBeenCalledWith(error)
    })

    it('should handle reconnection', () => {
      const reconnectHandler = vi.fn()
      mockSocket.on('reconnect', reconnectHandler)

      // Simulate reconnection
      mockSocket.connected = false
      mockSocket.connected = true
      mockSocket.on.mock.calls.find(call => call[0] === 'reconnect')?.[1]()

      expect(reconnectHandler).toHaveBeenCalled()
    })
  })

  describe('Performance and Reliability', () => {
    it('should handle high frequency cursor updates', () => {
      const cursorUpdates = Array.from({ length: 100 }, (_, i) => ({
        contractId: 'test-contract-1',
        position: i * 10,
      }))

      cursorUpdates.forEach(update => {
        mockSocket.emit('contract:cursor', update)
      })

      expect(mockSocket.emit).toHaveBeenCalledTimes(100)
    })

    it('should handle connection timeout', () => {
      const timeoutHandler = vi.fn()
      mockSocket.on('connect_timeout', timeoutHandler)

      // Simulate timeout
      mockSocket.on.mock.calls.find(call => call[0] === 'connect_timeout')?.[1]()

      expect(timeoutHandler).toHaveBeenCalled()
    })

    it('should handle ping/pong for connection health', () => {
      const pongHandler = vi.fn()
      mockSocket.on('pong', pongHandler)

      // Emit ping
      mockSocket.emit('ping')
      expect(mockSocket.emit).toHaveBeenCalledWith('ping')

      // Simulate pong response
      mockSocket.on.mock.calls.find(call => call[0] === 'pong')?.[1]()
      expect(pongHandler).toHaveBeenCalled()
    })
  })
})
