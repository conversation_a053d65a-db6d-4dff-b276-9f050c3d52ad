import { Server, Socket } from 'socket.io'
import { z } from 'zod'
import { prisma } from '@clm/db'
import { NotificationHandler } from './notifications'

const addCommentSchema = z.object({
  contractId: z.string(),
  content: z.string().min(1),
  parentId: z.string().optional(),
})

const updateCommentSchema = z.object({
  commentId: z.string(),
  content: z.string().min(1),
})

const deleteCommentSchema = z.object({
  commentId: z.string(),
})

const getCommentsSchema = z.object({
  contractId: z.string(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
})

export class CommentHandler {
  private notificationHandler: NotificationHandler

  constructor(private io: Server) {
    this.notificationHandler = new NotificationHandler(io)
  }

  handleConnection(socket: Socket) {
    const user = socket.data.user

    // Add comment
    socket.on('comment:add', async (data) => {
      try {
        const { contractId, content, parentId } = addCommentSchema.parse(data)
        await this.addComment(socket, contractId, content, parentId)
      } catch (error) {
        socket.emit('error', { message: 'Invalid comment data' })
      }
    })

    // Update comment
    socket.on('comment:update', async (data) => {
      try {
        const { commentId, content } = updateCommentSchema.parse(data)
        await this.updateComment(socket, commentId, content)
      } catch (error) {
        socket.emit('error', { message: 'Invalid comment update data' })
      }
    })

    // Delete comment
    socket.on('comment:delete', async (data) => {
      try {
        const { commentId } = deleteCommentSchema.parse(data)
        await this.deleteComment(socket, commentId)
      } catch (error) {
        socket.emit('error', { message: 'Invalid comment delete data' })
      }
    })

    // Get comments
    socket.on('comment:get', async (data) => {
      try {
        const { contractId, limit, offset } = getCommentsSchema.parse(data)
        await this.getComments(socket, contractId, limit, offset)
      } catch (error) {
        socket.emit('error', { message: 'Invalid get comments data' })
      }
    })

    // Join contract comments room
    socket.on('comment:join-contract', (data) => {
      try {
        const { contractId } = z.object({ contractId: z.string() }).parse(data)
        socket.join(`contract-comments:${contractId}`)
      } catch (error) {
        socket.emit('error', { message: 'Invalid contract ID' })
      }
    })

    // Leave contract comments room
    socket.on('comment:leave-contract', (data) => {
      try {
        const { contractId } = z.object({ contractId: z.string() }).parse(data)
        socket.leave(`contract-comments:${contractId}`)
      } catch (error) {
        socket.emit('error', { message: 'Invalid contract ID' })
      }
    })
  }

  private async addComment(socket: Socket, contractId: string, content: string, parentId?: string) {
    try {
      const user = socket.data.user

      // Check if contract exists and user has access
      const contract = await prisma.contract.findUnique({
        where: { id: contractId },
        select: { id: true, title: true, createdBy: true },
      })

      if (!contract) {
        socket.emit('error', { message: 'Contract not found' })
        return
      }

      // Check if parent comment exists (for replies)
      if (parentId) {
        const parentComment = await prisma.comment.findUnique({
          where: { id: parentId },
          select: { id: true, contractId: true },
        })

        if (!parentComment || parentComment.contractId !== contractId) {
          socket.emit('error', { message: 'Parent comment not found' })
          return
        }
      }

      // Create comment
      const comment = await prisma.comment.create({
        data: {
          content,
          contractId,
          authorId: user.id,
          parentId,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          parent: {
            select: {
              id: true,
              author: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      })

      // Broadcast to contract comments room
      this.io.to(`contract-comments:${contractId}`).emit('comment:added', {
        id: comment.id,
        content: comment.content,
        contractId: comment.contractId,
        parentId: comment.parentId,
        author: comment.author,
        parent: comment.parent,
        createdAt: comment.createdAt,
        updatedAt: comment.updatedAt,
      })

      // Send notification to contract participants
      await this.notificationHandler.notifyCommentAdded(
        contractId,
        contract.title,
        user.name,
        user.id
      )

      // If it's a reply, notify the parent comment author
      if (parentId && comment.parent?.author && comment.parent.author.id !== user.id) {
        await this.notificationHandler.sendToUser(comment.parent.author.id, {
          title: 'Balasan Komentar',
          message: `${user.name} membalas komentar Anda pada kontrak "${contract.title}"`,
          type: 'COMMENT_REPLY',
          contractId,
          isRead: false,
        })
      }

      // Check for mentions in comment content
      await this.processMentions(content, contractId, contract.title, user.id, user.name)

      socket.emit('comment:added-success', { commentId: comment.id })
    } catch (error) {
      console.error('Failed to add comment:', error)
      socket.emit('error', { message: 'Failed to add comment' })
    }
  }

  private async updateComment(socket: Socket, commentId: string, content: string) {
    try {
      const user = socket.data.user

      // Check if comment exists and user is the author
      const existingComment = await prisma.comment.findUnique({
        where: { id: commentId },
        include: {
          contract: {
            select: { id: true, title: true },
          },
        },
      })

      if (!existingComment) {
        socket.emit('error', { message: 'Comment not found' })
        return
      }

      if (existingComment.authorId !== user.id) {
        socket.emit('error', { message: 'You can only edit your own comments' })
        return
      }

      // Update comment
      const updatedComment = await prisma.comment.update({
        where: { id: commentId },
        data: { content },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
        },
      })

      // Broadcast to contract comments room
      this.io.to(`contract-comments:${existingComment.contractId}`).emit('comment:updated', {
        id: updatedComment.id,
        content: updatedComment.content,
        contractId: updatedComment.contractId,
        author: updatedComment.author,
        updatedAt: updatedComment.updatedAt,
      })

      // Process mentions in updated content
      await this.processMentions(
        content,
        existingComment.contractId,
        existingComment.contract.title,
        user.id,
        user.name
      )

      socket.emit('comment:updated-success', { commentId })
    } catch (error) {
      console.error('Failed to update comment:', error)
      socket.emit('error', { message: 'Failed to update comment' })
    }
  }

  private async deleteComment(socket: Socket, commentId: string) {
    try {
      const user = socket.data.user

      // Check if comment exists and user is the author or admin
      const existingComment = await prisma.comment.findUnique({
        where: { id: commentId },
        select: {
          id: true,
          authorId: true,
          contractId: true,
        },
      })

      if (!existingComment) {
        socket.emit('error', { message: 'Comment not found' })
        return
      }

      if (existingComment.authorId !== user.id && user.role !== 'ADMIN') {
        socket.emit('error', { message: 'You can only delete your own comments' })
        return
      }

      // Delete comment (this will also delete replies due to cascade)
      await prisma.comment.delete({
        where: { id: commentId },
      })

      // Broadcast to contract comments room
      this.io.to(`contract-comments:${existingComment.contractId}`).emit('comment:deleted', {
        commentId,
        contractId: existingComment.contractId,
      })

      socket.emit('comment:deleted-success', { commentId })
    } catch (error) {
      console.error('Failed to delete comment:', error)
      socket.emit('error', { message: 'Failed to delete comment' })
    }
  }

  private async getComments(socket: Socket, contractId: string, limit: number, offset: number) {
    try {
      // Check if contract exists
      const contract = await prisma.contract.findUnique({
        where: { id: contractId },
        select: { id: true },
      })

      if (!contract) {
        socket.emit('error', { message: 'Contract not found' })
        return
      }

      // Get comments
      const comments = await prisma.comment.findMany({
        where: {
          contractId,
          parentId: null, // Only top-level comments
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          replies: {
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  avatar: true,
                },
              },
            },
            orderBy: { createdAt: 'asc' },
          },
          _count: {
            select: {
              replies: true,
            },
          },
        },
      })

      socket.emit('comment:list', {
        contractId,
        comments: comments.map(comment => ({
          id: comment.id,
          content: comment.content,
          contractId: comment.contractId,
          author: comment.author,
          replies: comment.replies,
          replyCount: comment._count.replies,
          createdAt: comment.createdAt,
          updatedAt: comment.updatedAt,
        })),
        hasMore: comments.length === limit,
      })
    } catch (error) {
      console.error('Failed to get comments:', error)
      socket.emit('error', { message: 'Failed to get comments' })
    }
  }

  private async processMentions(
    content: string,
    contractId: string,
    contractTitle: string,
    authorId: string,
    authorName: string
  ) {
    try {
      // Extract mentions from content (e.g., @username or @email)
      const mentionRegex = /@(\w+(?:\.\w+)*@?\w*\.?\w*)/g
      const mentions = content.match(mentionRegex)

      if (!mentions) return

      // Find mentioned users
      const mentionedUsers = await prisma.user.findMany({
        where: {
          OR: mentions.map(mention => {
            const cleanMention = mention.substring(1) // Remove @
            return {
              OR: [
                { email: { contains: cleanMention, mode: 'insensitive' } },
                { name: { contains: cleanMention, mode: 'insensitive' } },
              ],
            }
          }),
          id: { not: authorId }, // Don't mention the author
        },
        select: { id: true, name: true, email: true },
      })

      // Send mention notifications
      for (const mentionedUser of mentionedUsers) {
        await this.notificationHandler.sendToUser(mentionedUser.id, {
          title: 'Anda Disebutkan',
          message: `${authorName} menyebutkan Anda dalam komentar pada kontrak "${contractTitle}"`,
          type: 'COMMENT_MENTION',
          contractId,
          isRead: false,
        })
      }
    } catch (error) {
      console.error('Failed to process mentions:', error)
    }
  }

  // Get comment statistics
  async getCommentStats(contractId: string) {
    try {
      const stats = await prisma.comment.aggregate({
        where: { contractId },
        _count: { id: true },
      })

      const recentComments = await prisma.comment.count({
        where: {
          contractId,
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      })

      return {
        total: stats._count.id,
        recent: recentComments,
      }
    } catch (error) {
      console.error('Failed to get comment stats:', error)
      return { total: 0, recent: 0 }
    }
  }
}
