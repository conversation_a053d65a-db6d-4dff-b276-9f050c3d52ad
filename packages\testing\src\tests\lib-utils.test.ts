import { describe, it, expect } from 'vitest'
import {
  formatCurrency,
  formatDate,
  validateEmail,
  validateNIK,
  validateNPWP,
  generateId,
  slugify,
  truncateText,
  isValidFileType,
  formatFileSize,
} from '@clm/lib'

describe('Utility Functions', () => {
  describe('formatCurrency', () => {
    it('should format IDR currency correctly', () => {
      expect(formatCurrency(1000000, 'IDR')).toBe('Rp1.000.000')
      expect(formatCurrency(50000, 'IDR')).toBe('Rp50.000')
      expect(formatCurrency(0, 'IDR')).toBe('Rp0')
    })

    it('should format USD currency correctly', () => {
      expect(formatCurrency(1000, 'USD')).toBe('$1,000.00')
      expect(formatCurrency(50.5, 'USD')).toBe('$50.50')
    })

    it('should handle negative amounts', () => {
      expect(formatCurrency(-1000, 'IDR')).toBe('-Rp1.000')
      expect(formatCurrency(-50.5, 'USD')).toBe('-$50.50')
    })

    it('should handle decimal amounts for IDR', () => {
      expect(formatCurrency(1000.50, 'IDR')).toBe('Rp1.001') // IDR rounds to nearest rupiah
    })
  })

  describe('formatDate', () => {
    const testDate = new Date('2024-01-15T10:30:00.000Z')

    it('should format date in Indonesian locale', () => {
      const formatted = formatDate(testDate, 'id-ID')
      expect(formatted).toMatch(/15\/01\/2024|15-01-2024/)
    })

    it('should format date with custom format', () => {
      const formatted = formatDate(testDate, 'id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
      expect(formatted).toContain('Januari')
      expect(formatted).toContain('2024')
      expect(formatted).toContain('15')
    })

    it('should handle string dates', () => {
      const formatted = formatDate('2024-01-15', 'id-ID')
      expect(formatted).toMatch(/15\/01\/2024|15-01-2024/)
    })

    it('should handle invalid dates', () => {
      expect(() => formatDate('invalid-date', 'id-ID')).toThrow()
    })
  })

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true)
      expect(validateEmail('<EMAIL>')).toBe(true)
      expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false)
      expect(validateEmail('user@')).toBe(false)
      expect(validateEmail('@domain.com')).toBe(false)
      expect(validateEmail('<EMAIL>')).toBe(false)
      expect(validateEmail('')).toBe(false)
    })

    it('should handle edge cases', () => {
      expect(validateEmail('<EMAIL>')).toBe(true) // Minimum valid email
      expect(validateEmail('user@domain')).toBe(false) // No TLD
      expect(validateEmail('user@domain.')).toBe(false) // Empty TLD
    })
  })

  describe('validateNIK', () => {
    it('should validate correct NIK format', () => {
      expect(validateNIK('3201234567890123')).toBe(true) // 16 digits
      expect(validateNIK('1234567890123456')).toBe(true)
    })

    it('should reject invalid NIK format', () => {
      expect(validateNIK('123456789012345')).toBe(false) // 15 digits
      expect(validateNIK('12345678901234567')).toBe(false) // 17 digits
      expect(validateNIK('320123456789012a')).toBe(false) // Contains letter
      expect(validateNIK('')).toBe(false)
      expect(validateNIK('0000000000000000')).toBe(false) // All zeros
    })
  })

  describe('validateNPWP', () => {
    it('should validate correct NPWP format', () => {
      expect(validateNPWP('12.345.678.9-012.345')).toBe(true)
      expect(validateNPWP('98.765.432.1-098.765')).toBe(true)
    })

    it('should validate NPWP without separators', () => {
      expect(validateNPWP('123456789012345')).toBe(true) // 15 digits
    })

    it('should reject invalid NPWP format', () => {
      expect(validateNPWP('12.345.678.9-012.34')).toBe(false) // Wrong format
      expect(validateNPWP('12345678901234')).toBe(false) // 14 digits
      expect(validateNPWP('1234567890123456')).toBe(false) // 16 digits
      expect(validateNPWP('12.345.678.a-012.345')).toBe(false) // Contains letter
      expect(validateNPWP('')).toBe(false)
    })
  })

  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId()
      const id2 = generateId()
      expect(id1).not.toBe(id2)
      expect(typeof id1).toBe('string')
      expect(id1.length).toBeGreaterThan(0)
    })

    it('should generate IDs with custom prefix', () => {
      const id = generateId('contract')
      expect(id).toMatch(/^contract_/)
    })

    it('should generate IDs with custom length', () => {
      const id = generateId('', 10)
      expect(id.length).toBe(10)
    })
  })

  describe('slugify', () => {
    it('should convert text to URL-friendly slug', () => {
      expect(slugify('Hello World')).toBe('hello-world')
      expect(slugify('Service Agreement Q1 2024')).toBe('service-agreement-q1-2024')
    })

    it('should handle special characters', () => {
      expect(slugify('Contract & Agreement')).toBe('contract-agreement')
      expect(slugify('NDA (Non-Disclosure Agreement)')).toBe('nda-non-disclosure-agreement')
    })

    it('should handle Indonesian characters', () => {
      expect(slugify('Kontrak Kerjasama')).toBe('kontrak-kerjasama')
      expect(slugify('Perjanjian Jual Beli')).toBe('perjanjian-jual-beli')
    })

    it('should handle multiple spaces and special cases', () => {
      expect(slugify('  Multiple   Spaces  ')).toBe('multiple-spaces')
      expect(slugify('---Already-Slugified---')).toBe('already-slugified')
      expect(slugify('')).toBe('')
    })
  })

  describe('truncateText', () => {
    const longText = 'This is a very long text that needs to be truncated for display purposes'

    it('should truncate text to specified length', () => {
      expect(truncateText(longText, 20)).toBe('This is a very long...')
      expect(truncateText(longText, 10)).toBe('This is a...')
    })

    it('should not truncate text shorter than limit', () => {
      const shortText = 'Short text'
      expect(truncateText(shortText, 20)).toBe(shortText)
    })

    it('should handle custom suffix', () => {
      expect(truncateText(longText, 20, ' [more]')).toBe('This is a very long [more]')
    })

    it('should handle edge cases', () => {
      expect(truncateText('', 10)).toBe('')
      expect(truncateText(longText, 0)).toBe('...')
      expect(truncateText(longText, -1)).toBe('...')
    })
  })

  describe('isValidFileType', () => {
    it('should validate allowed file types', () => {
      expect(isValidFileType('document.pdf', ['pdf', 'doc', 'docx'])).toBe(true)
      expect(isValidFileType('contract.docx', ['pdf', 'doc', 'docx'])).toBe(true)
      expect(isValidFileType('image.jpg', ['jpg', 'png', 'gif'])).toBe(true)
    })

    it('should reject disallowed file types', () => {
      expect(isValidFileType('script.exe', ['pdf', 'doc', 'docx'])).toBe(false)
      expect(isValidFileType('document.txt', ['pdf', 'doc', 'docx'])).toBe(false)
    })

    it('should handle case insensitive extensions', () => {
      expect(isValidFileType('document.PDF', ['pdf', 'doc', 'docx'])).toBe(true)
      expect(isValidFileType('contract.DOCX', ['pdf', 'doc', 'docx'])).toBe(true)
    })

    it('should handle files without extensions', () => {
      expect(isValidFileType('document', ['pdf', 'doc', 'docx'])).toBe(false)
      expect(isValidFileType('', ['pdf', 'doc', 'docx'])).toBe(false)
    })
  })

  describe('formatFileSize', () => {
    it('should format file sizes correctly', () => {
      expect(formatFileSize(1024)).toBe('1.0 KB')
      expect(formatFileSize(1048576)).toBe('1.0 MB')
      expect(formatFileSize(1073741824)).toBe('1.0 GB')
    })

    it('should handle bytes', () => {
      expect(formatFileSize(512)).toBe('512 B')
      expect(formatFileSize(0)).toBe('0 B')
    })

    it('should handle decimal places', () => {
      expect(formatFileSize(1536)).toBe('1.5 KB') // 1.5 * 1024
      expect(formatFileSize(2621440)).toBe('2.5 MB') // 2.5 * 1024 * 1024
    })

    it('should handle very large files', () => {
      expect(formatFileSize(1099511627776)).toBe('1.0 TB')
    })
  })
})
