{"name": "@clm/auth-service", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --port 3003", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit"}, "dependencies": {"better-auth": "^0.8.0", "next": "14.1.0", "react": "^18", "react-dom": "^18", "bcryptjs": "^2.4.3", "jose": "^5.2.0", "tailwindcss": "^3.4.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.33"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/bcryptjs": "^2.4.6", "eslint": "^8", "eslint-config-next": "14.1.0", "typescript": "^5"}}