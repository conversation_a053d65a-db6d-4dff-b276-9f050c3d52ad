import { Contract, ContractStatus, Priority, Prisma } from '@prisma/client'
import { BaseRepository, PaginationOptions, PaginationResult, FilterOptions, SortOptions } from './base'

export interface CreateContractInput {
  title: string
  description?: string
  content?: string
  fileUrl?: string
  fileName?: string
  fileSize?: number
  mimeType?: string
  status?: ContractStatus
  priority?: Priority
  value?: number
  currency?: string
  startDate?: Date
  endDate?: Date
  createdBy: string
  parties?: Array<{
    name: string
    email?: string
    phone?: string
    address?: string
    role: 'CLIENT' | 'VENDOR' | 'PARTNER' | 'INTERNAL'
  }>
}

export interface UpdateContractInput {
  title?: string
  description?: string
  content?: string
  fileUrl?: string
  fileName?: string
  fileSize?: number
  mimeType?: string
  status?: ContractStatus
  priority?: Priority
  value?: number
  currency?: string
  startDate?: Date
  endDate?: Date
}

export interface ContractFilters extends FilterOptions {
  status?: ContractStatus | ContractStatus[]
  priority?: Priority | Priority[]
  createdBy?: string
  dateRange?: { from: Date; to: Date }
  valueRange?: { from: number; to: number }
  search?: string
}

export interface ContractWithRelations extends Contract {
  creator: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  parties: Array<{
    id: string
    name: string
    email?: string
    role: string
  }>
  _count: {
    comments: number
    versions: number
    workflowSteps: number
  }
}

export class ContractsRepository extends BaseRepository<
  Contract,
  CreateContractInput,
  UpdateContractInput
> {
  async create(data: CreateContractInput): Promise<Contract> {
    try {
      const { parties, ...contractData } = data

      const contract = await this.prisma.contract.create({
        data: {
          ...contractData,
          parties: parties ? {
            create: parties,
          } : undefined,
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          parties: true,
        },
      })

      await this.createAuditLog(
        'CREATE',
        'Contract',
        contract.id,
        undefined,
        contractData,
        data.createdBy
      )

      return contract
    } catch (error) {
      this.handleError(error, 'create contract')
    }
  }

  async findById(id: string): Promise<ContractWithRelations | null> {
    try {
      return await this.prisma.contract.findUnique({
        where: { id },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          parties: true,
          comments: {
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  avatar: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 10, // Latest 10 comments
          },
          versions: {
            orderBy: { version: 'desc' },
            take: 5, // Latest 5 versions
          },
          aiAnalysis: true,
          workflowSteps: {
            include: {
              assignee: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: { order: 'asc' },
          },
          _count: {
            select: {
              comments: true,
              versions: true,
              workflowSteps: true,
            },
          },
        },
      }) as any
    } catch (error) {
      this.handleError(error, 'find contract by id')
    }
  }

  async findMany(
    options: PaginationOptions = {},
    filters: ContractFilters = {},
    sort?: SortOptions
  ): Promise<PaginationResult<ContractWithRelations>> {
    try {
      const where = this.buildContractFilters(filters)
      const orderBy = this.buildSort(sort)

      const query = {
        where,
        orderBy,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          parties: true,
          _count: {
            select: {
              comments: true,
              versions: true,
              workflowSteps: true,
            },
          },
        },
      }

      return this.paginate(Prisma.ModelName.Contract, query, options)
    } catch (error) {
      this.handleError(error, 'find contracts')
    }
  }

  async update(id: string, data: UpdateContractInput, userId?: string): Promise<Contract> {
    try {
      // Get current contract for audit
      const currentContract = await this.prisma.contract.findUnique({
        where: { id },
      })

      if (!currentContract) {
        throw new Error('Contract not found')
      }

      const contract = await this.prisma.contract.update({
        where: { id },
        data,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          parties: true,
        },
      })

      await this.createAuditLog(
        'UPDATE',
        'Contract',
        contract.id,
        currentContract,
        data,
        userId
      )

      return contract
    } catch (error) {
      this.handleError(error, 'update contract')
    }
  }

  async delete(id: string, userId?: string): Promise<boolean> {
    try {
      const contract = await this.prisma.contract.findUnique({
        where: { id },
      })

      if (!contract) {
        return false
      }

      await this.prisma.contract.delete({
        where: { id },
      })

      await this.createAuditLog(
        'DELETE',
        'Contract',
        id,
        contract,
        undefined,
        userId
      )

      return true
    } catch (error) {
      this.handleError(error, 'delete contract')
    }
  }

  async findByCreator(
    creatorId: string,
    options: PaginationOptions = {},
    filters: ContractFilters = {}
  ): Promise<PaginationResult<ContractWithRelations>> {
    return this.findMany(options, { ...filters, createdBy: creatorId })
  }

  async findByStatus(
    status: ContractStatus,
    options: PaginationOptions = {}
  ): Promise<PaginationResult<ContractWithRelations>> {
    return this.findMany(options, { status })
  }

  async updateStatus(
    id: string,
    status: ContractStatus,
    userId?: string
  ): Promise<Contract> {
    return this.update(id, { status }, userId)
  }

  async addComment(
    contractId: string,
    content: string,
    authorId: string,
    parentId?: string
  ): Promise<any> {
    try {
      const comment = await this.prisma.comment.create({
        data: {
          content,
          contractId,
          authorId,
          parentId,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
        },
      })

      await this.createAuditLog(
        'CREATE',
        'Comment',
        comment.id,
        undefined,
        { content, contractId },
        authorId
      )

      return comment
    } catch (error) {
      this.handleError(error, 'add comment')
    }
  }

  async getStatistics(userId?: string): Promise<{
    total: number
    byStatus: Record<ContractStatus, number>
    byPriority: Record<Priority, number>
    recentActivity: number
  }> {
    try {
      const where = userId ? { createdBy: userId } : {}

      const [
        total,
        statusCounts,
        priorityCounts,
        recentActivity,
      ] = await Promise.all([
        this.prisma.contract.count({ where }),
        this.prisma.contract.groupBy({
          by: ['status'],
          where,
          _count: { status: true },
        }),
        this.prisma.contract.groupBy({
          by: ['priority'],
          where,
          _count: { priority: true },
        }),
        this.prisma.contract.count({
          where: {
            ...where,
            updatedAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
            },
          },
        }),
      ])

      const byStatus = statusCounts.reduce((acc, item) => {
        acc[item.status] = item._count.status
        return acc
      }, {} as Record<ContractStatus, number>)

      const byPriority = priorityCounts.reduce((acc, item) => {
        acc[item.priority] = item._count.priority
        return acc
      }, {} as Record<Priority, number>)

      return {
        total,
        byStatus,
        byPriority,
        recentActivity,
      }
    } catch (error) {
      this.handleError(error, 'get statistics')
    }
  }

  private buildContractFilters(filters: ContractFilters): any {
    const where: any = {}

    if (filters.status) {
      where.status = Array.isArray(filters.status) 
        ? { in: filters.status }
        : filters.status
    }

    if (filters.priority) {
      where.priority = Array.isArray(filters.priority)
        ? { in: filters.priority }
        : filters.priority
    }

    if (filters.createdBy) {
      where.createdBy = filters.createdBy
    }

    if (filters.dateRange) {
      where.createdAt = {
        gte: filters.dateRange.from,
        lte: filters.dateRange.to,
      }
    }

    if (filters.valueRange) {
      where.value = {
        gte: filters.valueRange.from,
        lte: filters.valueRange.to,
      }
    }

    if (filters.search) {
      where.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
      ]
    }

    return where
  }
}

// Export singleton instance
export const contractsRepository = new ContractsRepository()
