# 📄 CLM Platform Indonesia

**Contract Lifecycle Management Platform** yang dirancang khusus untuk pasar Indonesia dengan kepatuhan penuh terhadap regulasi lokal (UU ITE, e-Meterai, Data Sovereignty).

![Development Status](https://img.shields.io/badge/Status-80%25%20Complete-green)
![TypeScript](https://img.shields.io/badge/TypeScript-100%25-blue)
![Testing](https://img.shields.io/badge/Testing-Vitest-yellow)
![License](https://img.shields.io/badge/License-MIT-blue)

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- MinIO (for file storage)

### Installation

```bash
# Clone repository
git clone https://github.com/rendoarsandi/CLMbeta.git
cd CLMbeta

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env.local

# Setup database
npm run db:migrate
npm run db:seed

# Start development servers
npm run dev
```

### Demo Accounts

```bash
Admin:    <EMAIL> / admin123    # Full system access
Legal:    <EMAIL> / legal123    # Legal team access
User:     <EMAIL> / user123      # Regular user access
```

### Application URLs

- **Dashboard**: http://localhost:3000 (Main application)
- **Admin Panel**: http://localhost:3001 (Admin-only access)
- **Landing Page**: http://localhost:3002 (Marketing site)
- **WebSocket Server**: http://localhost:3003 (Real-time server)

## 🏗️ Architecture

### Monorepo Structure
```
CLMbeta/
├── apps/
│   ├── dashboard/          # Main user interface (Next.js 14+)
│   ├── admin/             # Admin panel (Developer-only)
│   ├── landingpage/       # Marketing website
│   └── websocket/         # Real-time WebSocket server
│
├── packages/
│   ├── ui/                # shadcn/ui components
│   ├── auth/              # Better Auth integration
│   ├── testing/           # Vitest testing framework
│   ├── config/            # Shared configurations
│   ├── db/                # Prisma schema + repositories
│   ├── api/               # tRPC routers
│   └── lib/               # Utility functions
```

### Technology Stack

**Frontend**
- Next.js 14+ with App Router
- shadcn/ui + Tailwind CSS
- tRPC + React Query
- Better Auth
- Socket.io Client

**Backend**
- tRPC with TypeScript
- PostgreSQL + Prisma ORM
- Redis for caching
- MinIO for file storage
- Socket.io Server

**Development**
- Turborepo monorepo
- Vitest + React Testing Library
- TypeScript end-to-end
- ESLint + Prettier

## ✨ Features

### 🔐 Authentication & Security
- ✅ Better Auth integration with JWT
- ✅ Role-based access control (RBAC)
- ✅ Multi-factor authentication support
- ✅ Session management with Redis
- ✅ Security middleware and rate limiting

### 📄 Contract Management
- ✅ Contract CRUD operations
- ✅ Document upload and versioning
- ✅ Template management
- ✅ Advanced search and filtering
- ✅ Metadata extraction

### 👥 User Management
- ✅ User registration and profiles
- ✅ Role assignment (Admin, Legal, Finance, User, Viewer)
- ✅ User activation/deactivation
- ✅ Admin panel for user management
- ✅ Audit logging

### 🔄 Real-time Collaboration
- ✅ WebSocket server with Socket.io
- ✅ Real-time contract editing
- ✅ Live cursors and typing indicators
- ✅ Instant notifications
- ✅ User presence tracking
- ✅ Real-time commenting with mentions

### 🛠️ Admin Panel
- ✅ Developer-only secure access
- ✅ User management dashboard
- ✅ System banner management
- ✅ Issues and bug tracking
- ✅ System settings configuration
- ✅ Analytics and reporting

### 🧪 Testing & Quality
- ✅ Comprehensive test suite with Vitest
- ✅ API mocking with MSW
- ✅ Component testing with RTL
- ✅ Integration testing
- ✅ 80%+ test coverage

## 📋 Development Status

### ✅ Completed (80%)
- [x] Project foundation and infrastructure
- [x] Core architecture and shared packages
- [x] Authentication and security framework
- [x] Database and storage infrastructure
- [x] Core application development
- [x] Real-time collaboration features
- [x] Testing framework and quality assurance
- [x] Admin panel and user management

### 🚧 In Progress (20%)
- [ ] AI integration with Gemini API
- [ ] OpenSearch for advanced search
- [ ] Indonesian compliance (e-Meterai, PSrE)
- [ ] Production deployment setup

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm atau yarn
- Git

### Installation

1. Clone repository:
```bash
git clone https://github.com/rendoarsandi/CLMbeta.git
cd CLMbeta
```

2. Install dependencies:
```bash
npm install
```

3. Start development servers:
```bash
npm run dev
```

Aplikasi akan berjalan di:
- Dashboard: http://localhost:3000
- Admin Panel: http://localhost:3001
- Landing Page: http://localhost:3002
- WebSocket Server: http://localhost:3003

---

**CLM Platform Indonesia** - Revolutionizing contract management for Indonesian businesses with cutting-edge technology and full regulatory compliance.
