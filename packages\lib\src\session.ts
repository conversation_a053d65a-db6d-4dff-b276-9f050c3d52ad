import { nanoid } from 'nanoid'

export interface SessionData {
  userId: string
  email: string
  name: string
  role: string
  avatar?: string
  createdAt: number
  lastActivity: number
  ipAddress?: string
  userAgent?: string
}

export interface WebSocketSession extends SessionData {
  socketId: string
  connectionId: string
  rooms: string[]
}

// In-memory session store (in production, use Redis)
class SessionStore {
  private sessions = new Map<string, SessionData>()
  private userSessions = new Map<string, Set<string>>() // userId -> sessionIds
  private wsConnections = new Map<string, WebSocketSession>() // socketId -> session

  // HTTP Session management
  createSession(userId: string, userData: Omit<SessionData, 'userId' | 'createdAt' | 'lastActivity'>): string {
    const sessionId = nanoid(32)
    const now = Date.now()
    
    const sessionData: SessionData = {
      userId,
      ...userData,
      createdAt: now,
      lastActivity: now,
    }

    this.sessions.set(sessionId, sessionData)
    
    // Track user sessions
    if (!this.userSessions.has(userId)) {
      this.userSessions.set(userId, new Set())
    }
    this.userSessions.get(userId)!.add(sessionId)

    return sessionId
  }

  getSession(sessionId: string): SessionData | null {
    const session = this.sessions.get(sessionId)
    if (!session) return null

    // Check if session is expired (7 days)
    const maxAge = 7 * 24 * 60 * 60 * 1000 // 7 days
    if (Date.now() - session.createdAt > maxAge) {
      this.deleteSession(sessionId)
      return null
    }

    // Update last activity
    session.lastActivity = Date.now()
    return session
  }

  updateSession(sessionId: string, updates: Partial<SessionData>): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    Object.assign(session, updates, { lastActivity: Date.now() })
    return true
  }

  deleteSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    this.sessions.delete(sessionId)
    
    // Remove from user sessions
    const userSessions = this.userSessions.get(session.userId)
    if (userSessions) {
      userSessions.delete(sessionId)
      if (userSessions.size === 0) {
        this.userSessions.delete(session.userId)
      }
    }

    return true
  }

  getUserSessions(userId: string): SessionData[] {
    const sessionIds = this.userSessions.get(userId)
    if (!sessionIds) return []

    return Array.from(sessionIds)
      .map(id => this.sessions.get(id))
      .filter((session): session is SessionData => session !== undefined)
  }

  deleteUserSessions(userId: string): number {
    const sessionIds = this.userSessions.get(userId)
    if (!sessionIds) return 0

    let deletedCount = 0
    for (const sessionId of sessionIds) {
      if (this.sessions.delete(sessionId)) {
        deletedCount++
      }
    }

    this.userSessions.delete(userId)
    return deletedCount
  }

  // WebSocket Session management
  createWebSocketSession(
    socketId: string,
    sessionData: SessionData,
    connectionId?: string
  ): WebSocketSession {
    const wsSession: WebSocketSession = {
      ...sessionData,
      socketId,
      connectionId: connectionId || nanoid(16),
      rooms: [],
    }

    this.wsConnections.set(socketId, wsSession)
    return wsSession
  }

  getWebSocketSession(socketId: string): WebSocketSession | null {
    return this.wsConnections.get(socketId) || null
  }

  updateWebSocketSession(socketId: string, updates: Partial<WebSocketSession>): boolean {
    const session = this.wsConnections.get(socketId)
    if (!session) return false

    Object.assign(session, updates, { lastActivity: Date.now() })
    return true
  }

  deleteWebSocketSession(socketId: string): boolean {
    return this.wsConnections.delete(socketId)
  }

  joinRoom(socketId: string, room: string): boolean {
    const session = this.wsConnections.get(socketId)
    if (!session) return false

    if (!session.rooms.includes(room)) {
      session.rooms.push(room)
    }
    return true
  }

  leaveRoom(socketId: string, room: string): boolean {
    const session = this.wsConnections.get(socketId)
    if (!session) return false

    session.rooms = session.rooms.filter(r => r !== room)
    return true
  }

  getSocketsInRoom(room: string): WebSocketSession[] {
    return Array.from(this.wsConnections.values())
      .filter(session => session.rooms.includes(room))
  }

  getUserWebSocketSessions(userId: string): WebSocketSession[] {
    return Array.from(this.wsConnections.values())
      .filter(session => session.userId === userId)
  }

  // Cleanup expired sessions
  cleanup(): number {
    const now = Date.now()
    const maxAge = 7 * 24 * 60 * 60 * 1000 // 7 days
    const wsMaxAge = 24 * 60 * 60 * 1000 // 24 hours for WebSocket sessions
    
    let cleanedCount = 0

    // Cleanup HTTP sessions
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.createdAt > maxAge) {
        this.deleteSession(sessionId)
        cleanedCount++
      }
    }

    // Cleanup WebSocket sessions
    for (const [socketId, session] of this.wsConnections.entries()) {
      if (now - session.lastActivity > wsMaxAge) {
        this.wsConnections.delete(socketId)
        cleanedCount++
      }
    }

    return cleanedCount
  }

  // Statistics
  getStats() {
    return {
      totalSessions: this.sessions.size,
      totalWebSocketConnections: this.wsConnections.size,
      uniqueUsers: this.userSessions.size,
      averageSessionsPerUser: this.userSessions.size > 0 
        ? this.sessions.size / this.userSessions.size 
        : 0,
    }
  }
}

// Export singleton instance
export const sessionStore = new SessionStore()

// Utility functions
export function generateSessionToken(): string {
  return nanoid(32)
}

export function isSessionExpired(session: SessionData, maxAge = 7 * 24 * 60 * 60 * 1000): boolean {
  return Date.now() - session.createdAt > maxAge
}

export function isSessionActive(session: SessionData, inactivityThreshold = 30 * 60 * 1000): boolean {
  return Date.now() - session.lastActivity < inactivityThreshold
}

// Start cleanup interval
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    const cleaned = sessionStore.cleanup()
    if (cleaned > 0) {
      console.log(`Cleaned up ${cleaned} expired sessions`)
    }
  }, 60 * 60 * 1000) // Run every hour
}
