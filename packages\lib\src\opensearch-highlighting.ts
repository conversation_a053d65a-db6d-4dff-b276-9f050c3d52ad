import { opensearchClient } from './opensearch';

export async function searchWithHighlighting(query: string) {
  const response = await opensearchClient.search({
    index: 'contracts',
    body: {
      query: {
        multi_match: {
          query,
          fields: ['attachment.content', 'title', 'description'],
          fuzziness: 'AUTO',
        },
      },
      highlight: {
        fields: {
          'attachment.content': {},
        },
      },
    },
  });

  return response.body.hits.hits;
}
