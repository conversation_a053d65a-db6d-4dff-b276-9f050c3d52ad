import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { useAuth, usePermissions, signIn, signOut } from '@clm/auth'
import { renderWithProviders, mockUsers, createMockSession } from '../utils/test-helpers'

describe('Authentication System', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('useAuth hook', () => {
    it('should return null user when not authenticated', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: ({ children }) => renderWithProviders(<div>{children}</div>).container,
      })

      expect(result.current.user).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
    })

    it('should return user data when authenticated', async () => {
      const mockSession = createMockSession(mockUsers.admin)
      
      // Mock the session API call
      vi.mocked(fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockSession }),
      } as Response)

      const { result } = renderHook(() => useAuth(), {
        wrapper: ({ children }) => renderWithProviders(<div>{children}</div>, { user: mockUsers.admin }).container,
      })

      await waitFor(() => {
        expect(result.current.user).toEqual(mockUsers.admin)
        expect(result.current.isAuthenticated).toBe(true)
      })
    })
  })

  describe('usePermissions hook', () => {
    it('should return correct permissions for admin user', () => {
      const { result } = renderHook(() => usePermissions(), {
        wrapper: ({ children }) => renderWithProviders(<div>{children}</div>, { user: mockUsers.admin }).container,
      })

      expect(result.current.hasRole('ADMIN')).toBe(true)
      expect(result.current.canAccessAdmin()).toBe(true)
      expect(result.current.canManageUsers()).toBe(true)
      expect(result.current.canManageContracts()).toBe(true)
      expect(result.current.canCreateContracts()).toBe(true)
      expect(result.current.canApproveContracts()).toBe(true)
    })

    it('should return correct permissions for legal user', () => {
      const { result } = renderHook(() => usePermissions(), {
        wrapper: ({ children }) => renderWithProviders(<div>{children}</div>, { user: mockUsers.legal }).container,
      })

      expect(result.current.hasRole('LEGAL')).toBe(true)
      expect(result.current.canAccessAdmin()).toBe(true)
      expect(result.current.canManageUsers()).toBe(false)
      expect(result.current.canManageContracts()).toBe(true)
      expect(result.current.canCreateContracts()).toBe(true)
      expect(result.current.canApproveContracts()).toBe(true)
    })

    it('should return correct permissions for regular user', () => {
      const { result } = renderHook(() => usePermissions(), {
        wrapper: ({ children }) => renderWithProviders(<div>{children}</div>, { user: mockUsers.user }).container,
      })

      expect(result.current.hasRole('USER')).toBe(true)
      expect(result.current.canAccessAdmin()).toBe(false)
      expect(result.current.canManageUsers()).toBe(false)
      expect(result.current.canManageContracts()).toBe(false)
      expect(result.current.canCreateContracts()).toBe(true)
      expect(result.current.canApproveContracts()).toBe(false)
    })

    it('should return correct permissions for viewer user', () => {
      const { result } = renderHook(() => usePermissions(), {
        wrapper: ({ children }) => renderWithProviders(<div>{children}</div>, { user: mockUsers.viewer }).container,
      })

      expect(result.current.hasRole('VIEWER')).toBe(true)
      expect(result.current.canAccessAdmin()).toBe(false)
      expect(result.current.canManageUsers()).toBe(false)
      expect(result.current.canManageContracts()).toBe(false)
      expect(result.current.canCreateContracts()).toBe(false)
      expect(result.current.canApproveContracts()).toBe(false)
      expect(result.current.canViewContracts()).toBe(true)
    })

    it('should check contract-specific permissions correctly', () => {
      const { result } = renderHook(() => usePermissions(), {
        wrapper: ({ children }) => renderWithProviders(<div>{children}</div>, { user: mockUsers.user }).container,
      })

      // User can edit their own contracts
      expect(result.current.canEditContract('test-user-id')).toBe(true)
      // User cannot edit other's contracts
      expect(result.current.canEditContract('other-user-id')).toBe(false)
      
      // User can delete their own contracts
      expect(result.current.canDeleteContract('test-user-id')).toBe(true)
      // User cannot delete other's contracts
      expect(result.current.canDeleteContract('other-user-id')).toBe(false)
    })

    it('should allow admin to edit and delete any contract', () => {
      const { result } = renderHook(() => usePermissions(), {
        wrapper: ({ children }) => renderWithProviders(<div>{children}</div>, { user: mockUsers.admin }).container,
      })

      expect(result.current.canEditContract('any-user-id')).toBe(true)
      expect(result.current.canDeleteContract('any-user-id')).toBe(true)
    })
  })

  describe('Authentication functions', () => {
    it('should sign in successfully with valid credentials', async () => {
      vi.mocked(fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          data: {
            user: mockUsers.admin,
            session: createMockSession(mockUsers.admin).session,
          },
        }),
      } as Response)

      const result = await signIn.email({
        email: '<EMAIL>',
        password: 'admin123',
      })

      expect(result.data?.user).toEqual(mockUsers.admin)
      expect(result.error).toBeUndefined()
    })

    it('should fail to sign in with invalid credentials', async () => {
      vi.mocked(fetch).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({
          error: { message: 'Invalid credentials' },
        }),
      } as Response)

      const result = await signIn.email({
        email: '<EMAIL>',
        password: 'wrong-password',
      })

      expect(result.error).toBeDefined()
      expect(result.error?.message).toBe('Invalid credentials')
    })

    it('should sign out successfully', async () => {
      vi.mocked(fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: { success: true } }),
      } as Response)

      const result = await signOut()

      expect(result.data?.success).toBe(true)
      expect(result.error).toBeUndefined()
    })
  })

  describe('Role validation', () => {
    const roles = ['ADMIN', 'LEGAL', 'FINANCE', 'USER', 'VIEWER']

    it.each(roles)('should validate %s role correctly', (role) => {
      const user = { ...mockUsers.admin, role: role as any }
      const { result } = renderHook(() => usePermissions(), {
        wrapper: ({ children }) => renderWithProviders(<div>{children}</div>, { user }).container,
      })

      expect(result.current.hasRole(role)).toBe(true)
      expect(result.current.hasAnyRole([role])).toBe(true)
      expect(result.current.hasAnyRole(['OTHER_ROLE'])).toBe(false)
    })
  })

  describe('Error handling', () => {
    it('should handle network errors gracefully', async () => {
      vi.mocked(fetch).mockRejectedValueOnce(new Error('Network error'))

      const result = await signIn.email({
        email: '<EMAIL>',
        password: 'admin123',
      })

      expect(result.error).toBeDefined()
    })

    it('should handle invalid JSON responses', async () => {
      vi.mocked(fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => {
          throw new Error('Invalid JSON')
        },
      } as Response)

      const result = await signIn.email({
        email: '<EMAIL>',
        password: 'admin123',
      })

      expect(result.error).toBeDefined()
    })
  })
})
