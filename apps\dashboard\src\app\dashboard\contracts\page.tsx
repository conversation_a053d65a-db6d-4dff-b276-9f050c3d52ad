'use client'

import { useState } from 'react'
import { trpc } from '@/lib/trpc'
import { usePermissions } from '@clm/auth'
import { Button, Input, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@clm/ui'

export default function ContractsPage() {
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const permissions = usePermissions()

  const { data: contracts, isLoading, refetch } = trpc.contracts.getAll.useQuery({
    limit: 20,
    search: search || undefined,
    status: statusFilter || undefined,
  })

  const contractStatuses = [
    { value: '', label: 'Semua Status' },
    { value: 'DRAFT', label: 'Draft' },
    { value: 'REVIEW', label: 'Review' },
    { value: 'APPROVED', label: 'Disetujui' },
    { value: 'SIGNED', label: 'Di<PERSON><PERSON>ngani' },
    { value: 'ACTIVE', label: 'Aktif' },
    { value: 'EXPIRED', label: 'Kedaluwarsa' },
    { value: 'TERMINATED', label: 'Dihentikan' },
    { value: 'REJECTED', label: 'Ditolak' },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'bg-gray-100 text-gray-800'
      case 'REVIEW': return 'bg-yellow-100 text-yellow-800'
      case 'APPROVED': return 'bg-green-100 text-green-800'
      case 'SIGNED': return 'bg-blue-100 text-blue-800'
      case 'ACTIVE': return 'bg-emerald-100 text-emerald-800'
      case 'EXPIRED': return 'bg-red-100 text-red-800'
      case 'TERMINATED': return 'bg-red-100 text-red-800'
      case 'REJECTED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number, currency: string = 'IDR') => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency,
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Kontrak</h1>
          <p className="text-gray-600">Kelola semua kontrak Anda</p>
        </div>
        {permissions.canCreateContracts() && (
          <Button onClick={() => window.location.href = '/dashboard/contracts/new'}>
            📄 Buat Kontrak Baru
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Cari kontrak..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="w-full sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {contractStatuses.map((status) => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>
            <Button variant="outline" onClick={() => refetch()}>
              🔄 Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Contracts List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Card key={i}>
                <CardContent className="pt-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                    <div className="flex space-x-4">
                      <div className="h-3 bg-gray-200 rounded w-20"></div>
                      <div className="h-3 bg-gray-200 rounded w-24"></div>
                      <div className="h-3 bg-gray-200 rounded w-16"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : contracts?.contracts.length ? (
          contracts.contracts.map((contract) => (
            <Card key={contract.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {contract.title}
                      </h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(contract.status)}`}>
                        {contractStatuses.find(s => s.value === contract.status)?.label || contract.status}
                      </span>
                    </div>
                    
                    {contract.description && (
                      <p className="text-gray-600 mb-3 line-clamp-2">
                        {contract.description}
                      </p>
                    )}
                    
                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                      <span className="flex items-center">
                        👤 {contract.creator.name}
                      </span>
                      <span className="flex items-center">
                        📅 {new Date(contract.createdAt).toLocaleDateString('id-ID')}
                      </span>
                      {contract.value && (
                        <span className="flex items-center">
                          💰 {formatCurrency(contract.value, contract.currency)}
                        </span>
                      )}
                      <span className="flex items-center">
                        💬 {contract._count.comments} komentar
                      </span>
                      <span className="flex items-center">
                        📝 {contract._count.versions} versi
                      </span>
                    </div>
                    
                    {contract.parties.length > 0 && (
                      <div className="mt-3">
                        <p className="text-sm text-gray-500 mb-1">Pihak terlibat:</p>
                        <div className="flex flex-wrap gap-2">
                          {contract.parties.slice(0, 3).map((party) => (
                            <span
                              key={party.id}
                              className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded"
                            >
                              {party.name} ({party.role})
                            </span>
                          ))}
                          {contract.parties.length > 3 && (
                            <span className="px-2 py-1 bg-gray-50 text-gray-600 text-xs rounded">
                              +{contract.parties.length - 3} lainnya
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex flex-col space-y-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.location.href = `/dashboard/contracts/${contract.id}`}
                    >
                      👁️ Lihat
                    </Button>
                    {permissions.canEditContract(contract.createdBy) && (
                      <Button variant="outline" size="sm">
                        ✏️ Edit
                      </Button>
                    )}
                    {permissions.canDeleteContract(contract.createdBy) && (
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                        🗑️ Hapus
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📄</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Belum ada kontrak
                </h3>
                <p className="text-gray-500 mb-6">
                  {search || statusFilter 
                    ? 'Tidak ada kontrak yang sesuai dengan filter Anda.'
                    : 'Mulai dengan membuat kontrak pertama Anda.'
                  }
                </p>
                {permissions.canCreateContracts() && !search && !statusFilter && (
                  <Button onClick={() => window.location.href = '/dashboard/contracts/new'}>
                    📄 Buat Kontrak Pertama
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Pagination */}
      {contracts?.nextCursor && (
        <div className="flex justify-center">
          <Button variant="outline">
            Muat Lebih Banyak
          </Button>
        </div>
      )}
    </div>
  )
}
