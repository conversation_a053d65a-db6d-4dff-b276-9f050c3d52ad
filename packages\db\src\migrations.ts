import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export interface MigrationInfo {
  name: string
  description: string
  version: string
  executedAt?: Date
}

// Migration utilities
export class MigrationManager {
  async checkConnection(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1`
      return true
    } catch (error) {
      console.error('Database connection failed:', error)
      return false
    }
  }

  async createMigrationTable(): Promise<void> {
    try {
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS _migrations (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL UNIQUE,
          description TEXT,
          version VARCHAR(50) NOT NULL,
          executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `
      console.log('Migration table created successfully')
    } catch (error) {
      console.error('Failed to create migration table:', error)
      throw error
    }
  }

  async getExecutedMigrations(): Promise<MigrationInfo[]> {
    try {
      const migrations = await prisma.$queryRaw<MigrationInfo[]>`
        SELECT name, description, version, executed_at as "executedAt"
        FROM _migrations
        ORDER BY executed_at ASC
      `
      return migrations
    } catch (error) {
      console.error('Failed to get executed migrations:', error)
      return []
    }
  }

  async recordMigration(migration: Omit<MigrationInfo, 'executedAt'>): Promise<void> {
    try {
      await prisma.$executeRaw`
        INSERT INTO _migrations (name, description, version)
        VALUES (${migration.name}, ${migration.description}, ${migration.version})
      `
      console.log(`Migration ${migration.name} recorded successfully`)
    } catch (error) {
      console.error(`Failed to record migration ${migration.name}:`, error)
      throw error
    }
  }

  async isMigrationExecuted(migrationName: string): Promise<boolean> {
    try {
      const result = await prisma.$queryRaw<{ count: bigint }[]>`
        SELECT COUNT(*) as count
        FROM _migrations
        WHERE name = ${migrationName}
      `
      return Number(result[0].count) > 0
    } catch (error) {
      console.error(`Failed to check migration ${migrationName}:`, error)
      return false
    }
  }
}

// Predefined migrations
export const migrations: Array<{
  info: Omit<MigrationInfo, 'executedAt'>
  up: () => Promise<void>
  down: () => Promise<void>
}> = [
  {
    info: {
      name: '001_initial_schema',
      description: 'Create initial database schema with users, contracts, and workflows',
      version: '1.0.0',
    },
    up: async () => {
      // This would typically run Prisma migrate
      console.log('Running initial schema migration...')
      // In real implementation, this would execute the Prisma migration
    },
    down: async () => {
      console.log('Rolling back initial schema migration...')
      // Rollback logic
    },
  },
  {
    info: {
      name: '002_add_audit_indexes',
      description: 'Add indexes for audit log queries',
      version: '1.0.1',
    },
    up: async () => {
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_audit_logs_entity_id ON audit_logs(entity_id);
      `
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
      `
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
      `
      console.log('Audit log indexes created successfully')
    },
    down: async () => {
      await prisma.$executeRaw`DROP INDEX IF EXISTS idx_audit_logs_entity_id;`
      await prisma.$executeRaw`DROP INDEX IF EXISTS idx_audit_logs_user_id;`
      await prisma.$executeRaw`DROP INDEX IF EXISTS idx_audit_logs_created_at;`
      console.log('Audit log indexes dropped successfully')
    },
  },
  {
    info: {
      name: '003_add_contract_indexes',
      description: 'Add indexes for contract queries',
      version: '1.0.2',
    },
    up: async () => {
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_contracts_status ON contracts(status);
      `
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_contracts_created_by ON contracts(created_by);
      `
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_contracts_created_at ON contracts(created_at);
      `
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_contracts_title_search ON contracts USING gin(to_tsvector('english', title));
      `
      console.log('Contract indexes created successfully')
    },
    down: async () => {
      await prisma.$executeRaw`DROP INDEX IF EXISTS idx_contracts_status;`
      await prisma.$executeRaw`DROP INDEX IF EXISTS idx_contracts_created_by;`
      await prisma.$executeRaw`DROP INDEX IF EXISTS idx_contracts_created_at;`
      await prisma.$executeRaw`DROP INDEX IF EXISTS idx_contracts_title_search;`
      console.log('Contract indexes dropped successfully')
    },
  },
]

// Migration runner
export async function runMigrations(): Promise<void> {
  const manager = new MigrationManager()

  console.log('🔄 Starting database migrations...')

  // Check database connection
  const isConnected = await manager.checkConnection()
  if (!isConnected) {
    throw new Error('Cannot connect to database')
  }

  // Create migration table if it doesn't exist
  await manager.createMigrationTable()

  // Run pending migrations
  for (const migration of migrations) {
    const isExecuted = await manager.isMigrationExecuted(migration.info.name)
    
    if (!isExecuted) {
      console.log(`📦 Running migration: ${migration.info.name}`)
      try {
        await migration.up()
        await manager.recordMigration(migration.info)
        console.log(`✅ Migration ${migration.info.name} completed successfully`)
      } catch (error) {
        console.error(`❌ Migration ${migration.info.name} failed:`, error)
        throw error
      }
    } else {
      console.log(`⏭️  Migration ${migration.info.name} already executed`)
    }
  }

  console.log('✅ All migrations completed successfully')
}

// Rollback utility
export async function rollbackMigration(migrationName: string): Promise<void> {
  const manager = new MigrationManager()
  const migration = migrations.find(m => m.info.name === migrationName)

  if (!migration) {
    throw new Error(`Migration ${migrationName} not found`)
  }

  const isExecuted = await manager.isMigrationExecuted(migrationName)
  if (!isExecuted) {
    console.log(`Migration ${migrationName} was not executed`)
    return
  }

  console.log(`🔄 Rolling back migration: ${migrationName}`)
  try {
    await migration.down()
    await prisma.$executeRaw`DELETE FROM _migrations WHERE name = ${migrationName}`
    console.log(`✅ Migration ${migrationName} rolled back successfully`)
  } catch (error) {
    console.error(`❌ Rollback of ${migrationName} failed:`, error)
    throw error
  }
}

// Database health check
export async function healthCheck(): Promise<{
  database: boolean
  migrations: boolean
  tablesCount: number
}> {
  const manager = new MigrationManager()
  
  try {
    const database = await manager.checkConnection()
    
    if (!database) {
      return { database: false, migrations: false, tablesCount: 0 }
    }

    const executedMigrations = await manager.getExecutedMigrations()
    const allMigrationsUpToDate = executedMigrations.length === migrations.length

    const tablesResult = await prisma.$queryRaw<{ count: bigint }[]>`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = 'public'
    `
    const tablesCount = Number(tablesResult[0].count)

    return { database, migrations: allMigrationsUpToDate, tablesCount }
  } catch (error) {
    console.error('Health check failed:', error)
    return { database: false, migrations: false, tablesCount: 0 }
  }
}
