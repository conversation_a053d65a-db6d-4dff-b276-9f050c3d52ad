// Indonesian specific constants
export const INDONESIAN_PROVINCES = [
  'Ace<PERSON>',
  'Sumatera Utara',
  'Sumatera Barat',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON> Selat<PERSON>',
  'Bangka Belitung',
  '<PERSON><PERSON><PERSON><PERSON>',
  'Lam<PERSON>ng',
  'DKI Jakarta',
  'Jawa Barat',
  'Jawa Tengah',
  'DI Yogyakarta',
  'Jawa Timur',
  'Banten',
  'Bali',
  'Nusa Tenggara Barat',
  'Nusa Tenggara Timur',
  'Kalimantan Barat',
  'Kalimantan Tengah',
  'Kalimantan Selatan',
  'Kalimantan Timur',
  'Kalimantan Utara',
  'Sulawesi Utara',
  'Sulawesi Tengah',
  'Sulawesi Selatan',
  'Sulawesi Tenggara',
  'Gorontalo',
  'Sulawesi Barat',
  'Maluku',
  'Maluku Utara',
  'Papua',
  'Papua Barat',
  'Papua Selatan',
  'Papua Tengah',
  'Papua Pegunungan',
  'Papua Barat Daya',
] as const

export const CONTRACT_STATUSES = {
  DRAFT: 'Draft',
  REVIEW: 'Review',
  APPROVED: 'Disetujui',
  SIGNED: 'Ditandatangani',
  ACTIVE: 'Aktif',
  EXPIRED: 'Kedaluwarsa',
  TERMINATED: 'Dihentikan',
  REJECTED: 'Ditolak',
} as const

export const USER_ROLES = {
  ADMIN: 'Administrator',
  LEGAL: 'Legal',
  FINANCE: 'Keuangan',
  USER: 'Pengguna',
  VIEWER: 'Viewer',
} as const

export const PRIORITY_LEVELS = {
  LOW: 'Rendah',
  MEDIUM: 'Sedang',
  HIGH: 'Tinggi',
  URGENT: 'Mendesak',
} as const

export const NOTIFICATION_TYPES = {
  CONTRACT_CREATED: 'Kontrak Dibuat',
  CONTRACT_UPDATED: 'Kontrak Diperbarui',
  WORKFLOW_ASSIGNED: 'Tugas Workflow',
  WORKFLOW_COMPLETED: 'Workflow Selesai',
  COMMENT_ADDED: 'Komentar Baru',
  MENTION: 'Disebutkan',
  DEADLINE_REMINDER: 'Pengingat Deadline',
  SYSTEM_ALERT: 'Peringatan Sistem',
} as const

// File type constants
export const ALLOWED_FILE_TYPES = {
  DOCUMENTS: ['pdf', 'doc', 'docx', 'txt', 'rtf'],
  IMAGES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  SPREADSHEETS: ['xls', 'xlsx', 'csv'],
} as const

export const MAX_FILE_SIZE = {
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  IMAGE: 5 * 1024 * 1024, // 5MB
  SPREADSHEET: 10 * 1024 * 1024, // 10MB
} as const

// API endpoints
export const API_ENDPOINTS = {
  AUTH: '/api/auth',
  CONTRACTS: '/api/contracts',
  USERS: '/api/users',
  WORKFLOWS: '/api/workflows',
  TEMPLATES: '/api/templates',
  NOTIFICATIONS: '/api/notifications',
  FILES: '/api/files',
} as const

// Indonesian currency
export const CURRENCIES = {
  IDR: 'Rupiah',
  USD: 'US Dollar',
  EUR: 'Euro',
  SGD: 'Singapore Dollar',
  MYR: 'Malaysian Ringgit',
} as const
