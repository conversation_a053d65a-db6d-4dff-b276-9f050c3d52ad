// Main auth configuration
export { auth } from "./config"
export type { Session, User } from "./config"

// Client-side utilities
export {
  authClient,
  signIn,
  signUp,
  signOut,
  useSession,
  getSession,
  useAuth,
  requireAuth,
  isAuthError,
  getAuthErrorMessage,
} from "./client"

// Middleware (safe for both server and client)
export {
  authMiddleware,
  rateLimitMiddleware,
  securityHeadersMiddleware,
  middleware,
  config as middlewareConfig,
} from "./middleware"

// Components
export {
  AuthProvider,
  withAuth,
  PermissionGate,
} from "./components/auth-provider"

export { SignInForm } from "./components/sign-in-form"
