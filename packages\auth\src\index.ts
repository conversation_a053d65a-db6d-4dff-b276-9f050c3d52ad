import React from 'react'

// Minimal auth exports for now
export const auth = {
  api: {
    getSession: async () => ({ user: null }),
    signInEmail: async () => ({ user: null }),
    signUpEmail: async () => ({ user: null }),
    signOut: async () => ({}),
    updateUser: async () => ({ user: null }),
    changePassword: async () => ({}),
  },
  handler: () => {},
}

export const authClient = {
  useSession: () => ({ data: null, isPending: false }),
}

export const signIn = async () => {}
export const signUp = async () => {}
export const signOut = async () => {}
export const getSession = async () => ({ data: null })
export const useSession = () => ({ data: null, isPending: false })
export const useAuth = () => ({ user: null, session: null, isLoading: false, isAuthenticated: false })

// Placeholder components
export const AuthProvider = ({ children }: { children: React.ReactNode }) => children
export const SignInForm = () => React.createElement('div', null, 'Sign In Form Placeholder')

// Server utilities
export const getServerSession = async () => ({ user: null })
export const requireServerAuth = async () => ({ user: null })
export const requireServerRole = async (role: string) => ({ user: null })
export const requireServerAnyRole = async (roles: string[]) => ({ user: null })
