import { http, HttpResponse } from 'msw'

const mockNotifications = [
  {
    id: 'notification-1',
    title: 'Kontrak Baru Dibuat',
    message: 'Kontrak "Service Agreement Q1 2024" telah dibuat',
    type: 'CONTRACT_CREATED',
    userId: 'test-legal-id',
    contractId: 'test-contract-1',
    isRead: false,
    createdAt: '2024-01-01T10:00:00.000Z',
    contract: {
      id: 'test-contract-1',
      title: 'Service Agreement Q1 2024',
    },
  },
  {
    id: 'notification-2',
    title: 'Tugas Workflow Baru',
    message: 'Anda ditugaskan untuk "Legal Review" pada kontrak "Service Agreement Q1 2024"',
    type: 'WORKFLOW_ASSIGNED',
    userId: 'test-legal-id',
    contractId: 'test-contract-1',
    isRead: true,
    createdAt: '2024-01-01T09:00:00.000Z',
    contract: {
      id: 'test-contract-1',
      title: 'Service Agreement Q1 2024',
    },
  },
  {
    id: 'notification-3',
    title: 'Komentar Baru',
    message: 'Admin CLM menambahkan komentar pada kontrak "Service Agreement Q1 2024"',
    type: 'COMMENT_ADDED',
    userId: 'test-user-id',
    contractId: 'test-contract-1',
    isRead: false,
    createdAt: '2024-01-10T15:30:00.000Z',
    contract: {
      id: 'test-contract-1',
      title: 'Service Agreement Q1 2024',
    },
  },
]

export const notificationHandlers = [
  // Get notifications
  http.get('/api/trpc/notifications.getAll', ({ request }) => {
    const url = new URL(request.url)
    const input = JSON.parse(url.searchParams.get('input') || '{}')
    const { limit = 20, offset = 0, userId } = input

    let userNotifications = mockNotifications
    if (userId) {
      userNotifications = mockNotifications.filter(n => n.userId === userId)
    }

    const paginatedNotifications = userNotifications.slice(offset, offset + limit)

    return HttpResponse.json({
      result: {
        data: {
          notifications: paginatedNotifications,
          total: userNotifications.length,
          hasMore: offset + limit < userNotifications.length,
        },
      },
    })
  }),

  // Get unread count
  http.get('/api/trpc/notifications.getUnreadCount', ({ request }) => {
    const url = new URL(request.url)
    const input = JSON.parse(url.searchParams.get('input') || '{}')
    const { userId } = input

    const unreadCount = mockNotifications.filter(n => 
      n.userId === userId && !n.isRead
    ).length

    return HttpResponse.json({
      result: {
        data: { count: unreadCount },
      },
    })
  }),

  // Mark notification as read
  http.patch('/api/trpc/notifications.markAsRead', async ({ request }) => {
    const body = await request.json() as any
    const { notificationId } = body

    const notificationIndex = mockNotifications.findIndex(n => n.id === notificationId)

    if (notificationIndex === -1) {
      return HttpResponse.json(
        {
          error: {
            message: 'Notification not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    mockNotifications[notificationIndex].isRead = true

    return HttpResponse.json({
      result: {
        data: { success: true },
      },
    })
  }),

  // Mark all notifications as read
  http.patch('/api/trpc/notifications.markAllAsRead', async ({ request }) => {
    const body = await request.json() as any
    const { userId } = body

    mockNotifications.forEach(notification => {
      if (notification.userId === userId) {
        notification.isRead = true
      }
    })

    return HttpResponse.json({
      result: {
        data: { success: true },
      },
    })
  }),

  // Create notification
  http.post('/api/trpc/notifications.create', async ({ request }) => {
    const body = await request.json() as any
    const { title, message, type, userId, contractId } = body

    const newNotification = {
      id: `notification-${Date.now()}`,
      title,
      message,
      type,
      userId,
      contractId: contractId || null,
      isRead: false,
      createdAt: new Date().toISOString(),
      contract: contractId ? {
        id: contractId,
        title: 'Test Contract',
      } : null,
    }

    mockNotifications.push(newNotification)

    return HttpResponse.json({
      result: {
        data: newNotification,
      },
    })
  }),

  // Delete notification
  http.delete('/api/trpc/notifications.delete', ({ request }) => {
    const url = new URL(request.url)
    const input = JSON.parse(url.searchParams.get('input') || '{}')
    const { notificationId } = input

    const notificationIndex = mockNotifications.findIndex(n => n.id === notificationId)

    if (notificationIndex === -1) {
      return HttpResponse.json(
        {
          error: {
            message: 'Notification not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    mockNotifications.splice(notificationIndex, 1)

    return HttpResponse.json({
      result: {
        data: { success: true },
      },
    })
  }),

  // Get notification statistics
  http.get('/api/trpc/notifications.getStatistics', ({ request }) => {
    const url = new URL(request.url)
    const input = JSON.parse(url.searchParams.get('input') || '{}')
    const { userId } = input

    const userNotifications = mockNotifications.filter(n => n.userId === userId)
    
    const stats = {
      total: userNotifications.length,
      unread: userNotifications.filter(n => !n.isRead).length,
      read: userNotifications.filter(n => n.isRead).length,
      byType: {
        CONTRACT_CREATED: userNotifications.filter(n => n.type === 'CONTRACT_CREATED').length,
        CONTRACT_UPDATED: userNotifications.filter(n => n.type === 'CONTRACT_UPDATED').length,
        WORKFLOW_ASSIGNED: userNotifications.filter(n => n.type === 'WORKFLOW_ASSIGNED').length,
        COMMENT_ADDED: userNotifications.filter(n => n.type === 'COMMENT_ADDED').length,
        COMMENT_MENTION: userNotifications.filter(n => n.type === 'COMMENT_MENTION').length,
      },
      recent: userNotifications.slice(0, 5),
    }

    return HttpResponse.json({
      result: {
        data: stats,
      },
    })
  }),
]
