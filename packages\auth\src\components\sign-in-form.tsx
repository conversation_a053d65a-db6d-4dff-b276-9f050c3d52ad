"use client"

import React, { useState } from "react"
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation"
import { signIn } from "../client"
import { Button, Input, Card, CardContent, CardDescription, CardHeader, CardTitle } from "@clm/ui"

interface SignInFormProps {
  onSuccess?: () => void
  redirectTo?: string
  showDemoAccounts?: boolean
}

export function SignInForm({
  onSuccess,
  redirectTo,
  showDemoAccounts = true,
}: SignInFormProps) {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirect = redirectTo || searchParams.get("redirect") || "/dashboard"

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const result = await signIn.email({
        email,
        password,
      })

      if (result.error) {
        setError(result.error.message || "Sign in failed")
        return
      }

      // Success
      if (onSuccess) {
        onSuccess()
      } else {
        router.push(redirect)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Sign in failed")
    } finally {
      setIsLoading(false)
    }
  }

  const handleDemoLogin = async (demoEmail: string, demoPassword: string) => {
    setEmail(demoEmail)
    setPassword(demoPassword)
    setIsLoading(true)
    setError("")

    try {
      const result = await signIn.email({
        email: demoEmail,
        password: demoPassword,
      })

      if (result.error) {
        setError(result.error.message || "Demo login failed")
        return
      }

      if (onSuccess) {
        onSuccess()
      } else {
        router.push(redirect)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Demo login failed")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Masuk ke CLM Platform</CardTitle>
        <CardDescription>
          Masukkan email dan password untuk mengakses dashboard
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
              required
              disabled={isLoading}
              className="mt-1"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
              required
              disabled={isLoading}
              className="mt-1"
              placeholder="••••••••"
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? "Memproses..." : "Masuk"}
          </Button>
        </form>

        {showDemoAccounts && (
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-muted-foreground">
                  Demo Accounts
                </span>
              </div>
            </div>
            
            <div className="mt-4 space-y-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs"
                onClick={() => handleDemoLogin("<EMAIL>", "admin123")}
                disabled={isLoading}
              >
                🔑 Admin Demo (<EMAIL>)
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs"
                onClick={() => handleDemoLogin("<EMAIL>", "legal123")}
                disabled={isLoading}
              >
                ⚖️ Legal Demo (<EMAIL>)
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs"
                onClick={() => handleDemoLogin("<EMAIL>", "user123")}
                disabled={isLoading}
              >
                👤 User Demo (<EMAIL>)
              </Button>
            </div>
          </div>
        )}

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Belum punya akun?{" "}
            <button
              type="button"
              className="font-medium text-blue-600 hover:text-blue-500"
              onClick={() => router.push("/register")}
            >
              Daftar di sini
            </button>
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
