import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { mockContracts, mockUsers, renderWithProviders } from '../utils/test-helpers'

// Mock tRPC client
const mockTrpcClient = {
  contracts: {
    getAll: {
      useQuery: vi.fn(),
    },
    getById: {
      useQuery: vi.fn(),
    },
    create: {
      useMutation: vi.fn(),
    },
    update: {
      useMutation: vi.fn(),
    },
    delete: {
      useMutation: vi.fn(),
    },
    getStatistics: {
      useQuery: vi.fn(),
    },
  },
}

vi.mock('@/lib/trpc', () => ({
  trpc: mockTrpcClient,
}))

describe('Contract Management', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Contract Queries', () => {
    it('should fetch all contracts successfully', async () => {
      const mockData = {
        contracts: [mockContracts.active, mockContracts.draft],
        nextCursor: null,
      }

      mockTrpcClient.contracts.getAll.useQuery.mockReturnValue({
        data: mockData,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      const { result } = renderHook(
        () => mockTrpcClient.contracts.getAll.useQuery({ limit: 10 }),
        {
          wrapper: ({ children }) => {
            const queryClient = new QueryClient({
              defaultOptions: { queries: { retry: false } },
            })
            return (
              <QueryClientProvider client={queryClient}>
                {children}
              </QueryClientProvider>
            )
          },
        }
      )

      expect(result.current.data).toEqual(mockData)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
    })

    it('should handle contract query with filters', () => {
      const mockData = {
        contracts: [mockContracts.active],
        nextCursor: null,
      }

      mockTrpcClient.contracts.getAll.useQuery.mockReturnValue({
        data: mockData,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      })

      const { result } = renderHook(
        () => mockTrpcClient.contracts.getAll.useQuery({
          limit: 10,
          status: 'ACTIVE',
          search: 'Service Agreement',
        }),
        {
          wrapper: ({ children }) => {
            const queryClient = new QueryClient({
              defaultOptions: { queries: { retry: false } },
            })
            return (
              <QueryClientProvider client={queryClient}>
                {children}
              </QueryClientProvider>
            )
          },
        }
      )

      expect(mockTrpcClient.contracts.getAll.useQuery).toHaveBeenCalledWith({
        limit: 10,
        status: 'ACTIVE',
        search: 'Service Agreement',
      })
    })

    it('should fetch contract by ID successfully', () => {
      mockTrpcClient.contracts.getById.useQuery.mockReturnValue({
        data: mockContracts.active,
        isLoading: false,
        error: null,
      })

      const { result } = renderHook(
        () => mockTrpcClient.contracts.getById.useQuery({ id: 'test-contract-1' }),
        {
          wrapper: ({ children }) => {
            const queryClient = new QueryClient({
              defaultOptions: { queries: { retry: false } },
            })
            return (
              <QueryClientProvider client={queryClient}>
                {children}
              </QueryClientProvider>
            )
          },
        }
      )

      expect(result.current.data).toEqual(mockContracts.active)
      expect(result.current.isLoading).toBe(false)
    })

    it('should handle contract not found error', () => {
      const mockError = new Error('Contract not found')
      
      mockTrpcClient.contracts.getById.useQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: mockError,
      })

      const { result } = renderHook(
        () => mockTrpcClient.contracts.getById.useQuery({ id: 'non-existent-id' }),
        {
          wrapper: ({ children }) => {
            const queryClient = new QueryClient({
              defaultOptions: { queries: { retry: false } },
            })
            return (
              <QueryClientProvider client={queryClient}>
                {children}
              </QueryClientProvider>
            )
          },
        }
      )

      expect(result.current.error).toEqual(mockError)
      expect(result.current.data).toBeNull()
    })
  })

  describe('Contract Mutations', () => {
    it('should create contract successfully', async () => {
      const mockMutate = vi.fn()
      const mockMutateAsync = vi.fn().mockResolvedValue(mockContracts.draft)

      mockTrpcClient.contracts.create.useMutation.mockReturnValue({
        mutate: mockMutate,
        mutateAsync: mockMutateAsync,
        isLoading: false,
        error: null,
        data: mockContracts.draft,
      })

      const { result } = renderHook(
        () => mockTrpcClient.contracts.create.useMutation(),
        {
          wrapper: ({ children }) => {
            const queryClient = new QueryClient({
              defaultOptions: { queries: { retry: false } },
            })
            return (
              <QueryClientProvider client={queryClient}>
                {children}
              </QueryClientProvider>
            )
          },
        }
      )

      const contractData = {
        title: 'New Contract',
        description: 'Test contract',
        priority: 'MEDIUM' as const,
        parties: [],
      }

      await result.current.mutateAsync(contractData)

      expect(mockMutateAsync).toHaveBeenCalledWith(contractData)
    })

    it('should handle contract creation error', async () => {
      const mockError = new Error('Failed to create contract')
      const mockMutateAsync = vi.fn().mockRejectedValue(mockError)

      mockTrpcClient.contracts.create.useMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutateAsync,
        isLoading: false,
        error: mockError,
        data: null,
      })

      const { result } = renderHook(
        () => mockTrpcClient.contracts.create.useMutation(),
        {
          wrapper: ({ children }) => {
            const queryClient = new QueryClient({
              defaultOptions: { queries: { retry: false } },
            })
            return (
              <QueryClientProvider client={queryClient}>
                {children}
              </QueryClientProvider>
            )
          },
        }
      )

      await expect(result.current.mutateAsync({})).rejects.toThrow('Failed to create contract')
    })

    it('should update contract successfully', async () => {
      const updatedContract = {
        ...mockContracts.draft,
        title: 'Updated Contract Title',
        status: 'REVIEW' as const,
      }

      const mockMutateAsync = vi.fn().mockResolvedValue(updatedContract)

      mockTrpcClient.contracts.update.useMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutateAsync,
        isLoading: false,
        error: null,
        data: updatedContract,
      })

      const { result } = renderHook(
        () => mockTrpcClient.contracts.update.useMutation(),
        {
          wrapper: ({ children }) => {
            const queryClient = new QueryClient({
              defaultOptions: { queries: { retry: false } },
            })
            return (
              <QueryClientProvider client={queryClient}>
                {children}
              </QueryClientProvider>
            )
          },
        }
      )

      const updateData = {
        id: 'test-contract-2',
        title: 'Updated Contract Title',
        status: 'REVIEW' as const,
      }

      await result.current.mutateAsync(updateData)

      expect(mockMutateAsync).toHaveBeenCalledWith(updateData)
    })

    it('should delete contract successfully', async () => {
      const mockMutateAsync = vi.fn().mockResolvedValue({ success: true })

      mockTrpcClient.contracts.delete.useMutation.mockReturnValue({
        mutate: vi.fn(),
        mutateAsync: mockMutateAsync,
        isLoading: false,
        error: null,
        data: { success: true },
      })

      const { result } = renderHook(
        () => mockTrpcClient.contracts.delete.useMutation(),
        {
          wrapper: ({ children }) => {
            const queryClient = new QueryClient({
              defaultOptions: { queries: { retry: false } },
            })
            return (
              <QueryClientProvider client={queryClient}>
                {children}
              </QueryClientProvider>
            )
          },
        }
      )

      await result.current.mutateAsync({ id: 'test-contract-1' })

      expect(mockMutateAsync).toHaveBeenCalledWith({ id: 'test-contract-1' })
    })
  })

  describe('Contract Statistics', () => {
    it('should fetch contract statistics successfully', () => {
      const mockStats = {
        total: 2,
        byStatus: {
          DRAFT: 1,
          REVIEW: 0,
          APPROVED: 0,
          SIGNED: 0,
          ACTIVE: 1,
          EXPIRED: 0,
          TERMINATED: 0,
          REJECTED: 0,
        },
        totalValue: 50000000,
        recentActivity: [mockContracts.active],
      }

      mockTrpcClient.contracts.getStatistics.useQuery.mockReturnValue({
        data: mockStats,
        isLoading: false,
        error: null,
      })

      const { result } = renderHook(
        () => mockTrpcClient.contracts.getStatistics.useQuery(),
        {
          wrapper: ({ children }) => {
            const queryClient = new QueryClient({
              defaultOptions: { queries: { retry: false } },
            })
            return (
              <QueryClientProvider client={queryClient}>
                {children}
              </QueryClientProvider>
            )
          },
        }
      )

      expect(result.current.data).toEqual(mockStats)
      expect(result.current.isLoading).toBe(false)
    })
  })

  describe('Contract Validation', () => {
    it('should validate required contract fields', () => {
      const validContract = {
        title: 'Valid Contract',
        description: 'Valid description',
        priority: 'MEDIUM' as const,
        parties: [
          {
            name: 'PT. Test Company',
            email: '<EMAIL>',
            role: 'CLIENT' as const,
          },
        ],
      }

      expect(validContract.title).toBeTruthy()
      expect(validContract.title.length).toBeGreaterThan(0)
      expect(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).toContain(validContract.priority)
    })

    it('should validate contract parties', () => {
      const validParty = {
        name: 'PT. Test Company',
        email: '<EMAIL>',
        phone: '+62-21-1234567',
        address: 'Jakarta, Indonesia',
        role: 'CLIENT' as const,
      }

      expect(validParty.name).toBeTruthy()
      expect(validParty.email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
      expect(['CLIENT', 'VENDOR', 'PARTNER', 'INTERNAL']).toContain(validParty.role)
    })

    it('should validate contract dates', () => {
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-12-31')

      expect(endDate.getTime()).toBeGreaterThan(startDate.getTime())
    })

    it('should validate contract value', () => {
      const contractValue = 50000000
      const currency = 'IDR'

      expect(contractValue).toBeGreaterThan(0)
      expect(['IDR', 'USD', 'EUR', 'SGD']).toContain(currency)
    })
  })

  describe('Contract Status Transitions', () => {
    const validTransitions = {
      DRAFT: ['REVIEW', 'REJECTED'],
      REVIEW: ['APPROVED', 'REJECTED', 'DRAFT'],
      APPROVED: ['SIGNED', 'REJECTED'],
      SIGNED: ['ACTIVE'],
      ACTIVE: ['EXPIRED', 'TERMINATED'],
      EXPIRED: [],
      TERMINATED: [],
      REJECTED: ['DRAFT'],
    }

    it.each(Object.entries(validTransitions))(
      'should allow valid transitions from %s',
      (fromStatus, toStatuses) => {
        toStatuses.forEach(toStatus => {
          expect(toStatuses).toContain(toStatus)
        })
      }
    )

    it('should prevent invalid status transitions', () => {
      // ACTIVE cannot go directly to DRAFT
      expect(validTransitions.ACTIVE).not.toContain('DRAFT')
      
      // EXPIRED cannot transition to any other status
      expect(validTransitions.EXPIRED).toHaveLength(0)
      
      // TERMINATED cannot transition to any other status
      expect(validTransitions.TERMINATED).toHaveLength(0)
    })
  })
})
