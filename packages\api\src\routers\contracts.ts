import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure } from '../trpc'
import { prisma, ContractStatus, Priority } from '@clm/db'

// Import new services from the lib package
// TODO: Uncomment when dependencies are available
// import { 
//   searchContracts,
//   summarizeContract,
//   extractDetailedMetadata,
//   analyzeRisk,
//   startSignatureProcess,
//   ensureEMeteraiCompliance 
// } from '@clm/lib'

// Helper function to get contract content. In a real app, this might come from MinIO.
const getContractContent = async (contractId: string) => {
  const contract = await prisma.contract.findUnique({
    where: { id: contractId },
    select: { content: true },
  });
  if (!contract || !contract.content) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'Contract content not found.',
    });
  }
  return contract.content;
};

export const contractsRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(10),
        cursor: z.string().optional(),
        status: z.nativeEnum(ContractStatus).optional(),
        search: z.string().optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      const { limit, cursor, status, search } = input

      const where: any = {}
      if (status) {
        where.status = status
      }
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' as const } },
          { description: { contains: search, mode: 'insensitive' as const } },
        ]
      }

      const contracts = await prisma.contract.findMany({
        where,
        take: (limit as number) + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: { createdAt: 'desc' },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          parties: true,
          _count: {
            select: {
              comments: true,
              versions: true,
            },
          },
        },
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (contracts.length > (limit as number)) {
        const nextItem = contracts.pop()
        nextCursor = nextItem!.id
      }

      return {
        contracts,
        nextCursor,
      }
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const contract = await prisma.contract.findUnique({
        where: { id: input.id },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          parties: true,
          comments: {
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  avatar: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
          },
          versions: {
            orderBy: { version: 'desc' },
          },
          aiAnalysis: true,
          workflowSteps: {
            include: {
              assignee: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: { order: 'asc' },
          },
        },
      })

      if (!contract) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Contract not found',
        })
      }

      return contract
    }),

  create: protectedProcedure
    .input(
      z.object({
        title: z.string().min(1),
        description: z.string().optional(),
        content: z.string().optional(),
        priority: z.nativeEnum(Priority).default('MEDIUM'),
        value: z.number().optional(),
        currency: z.string().default('IDR'),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        parties: z.array(
          z.object({
            name: z.string(),
            email: z.string().email().optional(),
            phone: z.string().optional(),
            address: z.string().optional(),
            role: z.enum(['CLIENT', 'VENDOR', 'PARTNER', 'INTERNAL']),
          })
        ).optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const { parties, ...contractData } = input

      const contract = await prisma.contract.create({
        data: {
          ...contractData,
          createdBy: ctx.session.user.id,
          parties: parties ? {
            create: parties,
          } : undefined,
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          parties: true,
        },
      })

      // Create audit log
      await prisma.auditLog.create({
        data: {
          action: 'CREATE',
          entity: 'Contract',
          entityId: contract.id,
          newValues: contractData,
          userId: ctx.session.user.id,
          contractId: contract.id,
        },
      })

      return contract
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        title: z.string().min(1).optional(),
        description: z.string().optional(),
        content: z.string().optional(),
        status: z.nativeEnum(ContractStatus).optional(),
        priority: z.nativeEnum(Priority).optional(),
        value: z.number().optional(),
        currency: z.string().optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const { id, ...updateData } = input

      // Get current contract for audit
      const currentContract = await prisma.contract.findUnique({
        where: { id },
      })

      if (!currentContract) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Contract not found',
        })
      }

      const contract = await prisma.contract.update({
        where: { id },
        data: updateData,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          parties: true,
        },
      })

      // Create audit log
      await prisma.auditLog.create({
        data: {
          action: 'UPDATE',
          entity: 'Contract',
          entityId: contract.id,
          oldValues: currentContract,
          newValues: updateData,
          userId: ctx.session.user.id,
          contractId: contract.id,
        },
      })

      return contract
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const contract = await prisma.contract.findUnique({
        where: { id: input.id },
      })

      if (!contract) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Contract not found',
        })
      }

      await prisma.contract.delete({
        where: { id: input.id },
      })

      // Create audit log
      await prisma.auditLog.create({
        data: {
          action: 'DELETE',
          entity: 'Contract',
          entityId: contract.id,
          oldValues: contract,
          userId: ctx.session.user.id,
        },
      })

      return { success: true }
    }),

  // --- NEW PROCEDURES ---
  // TODO: Uncomment when dependencies are available

  // search: protectedProcedure
  //   .input(z.object({ query: z.string() }))
  //   .query(async ({ input }) => {
  //     // In a real implementation, we would need to handle the results,
  //     // which are OpenSearch hits, and map them to our contract type.
  //     // For now, we just return the raw hits.
  //     const results = await searchContracts(input.query);
  //     return results;
  //   }),

  // summarize: protectedProcedure
  //   .input(z.object({ contractId: z.string() }))
  //   .mutation(async ({ input }) => {
  //     const content = await getContractContent(input.contractId);
  //     const summary = await summarizeContract(content);
  //     // Here you would typically save the summary to the database
  //     // e.g., in the AiAnalysis model
  //     await prisma.aiAnalysis.upsert({
  //       where: { contractId: input.contractId },
  //       update: { summary },
  //       create: {
  //         contractId: input.contractId,
  //         summary,
  //       }
  //     });
  //     return { summary };
  //   }),

  // extractMetadata: protectedProcedure
  //   .input(z.object({ contractId: z.string() }))
  //   .mutation(async ({ input }) => {
  //     const content = await getContractContent(input.contractId);
  //     const metadata = await extractDetailedMetadata(content);
  //     // Here you would save the extracted metadata to the database
  //     return { metadata };
  //   }),

  // analyzeRisk: protectedProcedure
  //   .input(z.object({ contractId: z.string() }))
  //   .mutation(async ({ input }) => {
  //     const content = await getContractContent(input.contractId);
  //     const riskAnalysis = await analyzeRisk(content);
  //      // Save the analysis to the database
  //      await prisma.aiAnalysis.upsert({
  //       where: { contractId: input.contractId },
  //       update: { riskAnalysis },
  //       create: {
  //         contractId: input.contractId,
  //         riskAnalysis,
  //       }
  //     });
  //     return { riskAnalysis };
  //   }),

  // sign: protectedProcedure
  //   .input(z.object({
  //     contractId: z.string(),
  //     recipient: z.object({
  //       name: z.string(),
  //       email: z.string().email(),
  //     }),
  //   }))
  //   .mutation(async ({ input }) => {
  //     // This is a mock. In a real app, you'd fetch the document from storage (e.g., MinIO)
  //     const mockDocumentBuffer = Buffer.from('This is a mock contract document.');
  //     const status = await startSignatureProcess(mockDocumentBuffer, input.recipient);
  //     // Update contract status in DB
  //     if (status === 'signed') {
  //       await prisma.contract.update({
  //         where: { id: input.contractId },
  //         data: { status: 'SIGNED' },
  //       });
  //     }
  //     return { status };
  //   }),

  // TODO: Comment out until dependencies are available
  // applyMeterai: protectedProcedure
  //   .input(z.object({ contractId: z.string() }))
  //   .mutation(async ({ input }) => {
  //     // This is a mock. In a real app, you'd fetch the document from storage.
  //     const mockDocumentBuffer = Buffer.from('This is a mock contract document for e-Meterai.');
  //     const documentId = await ensureEMeteraiCompliance(mockDocumentBuffer);
  //     // Update contract in DB with meterai info
  //     return { success: true, documentId };
  //   }),

  getStatistics: protectedProcedure
    .query(async () => {
      const [total, draft, review, active, expired] = await Promise.all([
        prisma.contract.count(),
        prisma.contract.count({ where: { status: ContractStatus.DRAFT } }),
        prisma.contract.count({ where: { status: ContractStatus.REVIEW } }),
        prisma.contract.count({ where: { status: ContractStatus.ACTIVE } }),
        prisma.contract.count({ where: { status: ContractStatus.EXPIRED } }),
      ])

      return {
        total,
        byStatus: {
          draft,
          review,
          active,
          expired,
        }
      }
    }),
})
