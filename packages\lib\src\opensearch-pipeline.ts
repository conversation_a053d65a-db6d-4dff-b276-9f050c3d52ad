import { opensearchClient } from './opensearch';

export async function createIngestPipeline() {
  const pipeline = {
    id: 'contract-pipeline',
    body: {
      description: 'Pipeline for ingesting and processing contract documents',
      processors: [
        {
          attachment: {
            field: 'data',
            target_field: 'attachment',
            indexed_chars: -1,
          },
        },
        {
          remove: {
            field: 'data',
          },
        },
      ],
    },
  };

  await opensearchClient.ingest.putPipeline(pipeline);
}
