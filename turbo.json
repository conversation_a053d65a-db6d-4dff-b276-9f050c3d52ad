{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "start": {"dependsOn": ["build"]}, "lint": {"dependsOn": ["^lint"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "type-check": {"dependsOn": ["^type-check"]}, "clean": {"cache": false}}}