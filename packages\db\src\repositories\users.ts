import { User, User<PERSON><PERSON>, Prisma } from '@prisma/client'
import { BaseRepository, PaginationOptions, PaginationResult, FilterOptions } from './base'
import bcrypt from 'bcryptjs'

export interface CreateUserInput {
  email: string
  name: string
  password: string
  role?: UserRole
  avatar?: string
}

export interface UpdateUserInput {
  name?: string
  role?: UserRole
  avatar?: string
  isActive?: boolean
}

export interface UserFilters extends FilterOptions {
  role?: UserRole | UserRole[]
  isActive?: boolean
  search?: string
}

export interface SafeUser extends Omit<User, 'password'> {
  _count?: {
    contracts: number
    comments: number
    workflowSteps: number
  }
}

export class UsersRepository extends BaseRepository<
  User,
  CreateUserInput,
  UpdateUserInput
> {
  async create(data: CreateUserInput): Promise<SafeUser> {
    try {
      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email: data.email },
      })

      if (existingUser) {
        throw new Error('User with this email already exists')
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, 12)

      const user = await this.prisma.user.create({
        data: {
          email: data.email,
          name: data.name,
          role: data.role,
          avatar: data.avatar,
        },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          avatar: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      })

      await this.createAuditLog(
        'CREATE',
        'User',
        user.id,
        undefined,
        { email: data.email, name: data.name, role: data.role }
      )

      return user
    } catch (error) {
      this.handleError(error, 'create user')
    }
  }

  async findById(id: string): Promise<SafeUser | null> {
    try {
      return await this.prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          avatar: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              contracts: true,
              comments: true,
              workflowSteps: true,
            },
          },
        },
      })
    } catch (error) {
      this.handleError(error, 'find user by id')
    }
  }

  async findByEmail(email: string): Promise<User | null> {
    try {
      return await this.prisma.user.findUnique({
        where: { email },
      })
    } catch (error) {
      this.handleError(error, 'find user by email')
    }
  }

  async findMany(
    options: PaginationOptions = {},
    filters: UserFilters = {}
  ): Promise<PaginationResult<SafeUser>> {
    try {
      const where = this.buildUserFilters(filters)

      const query = {
        where,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          avatar: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              contracts: true,
              comments: true,
              workflowSteps: true,
            },
          },
        },
      }

      return this.paginate(Prisma.ModelName.User, query, options)
    } catch (error) {
      this.handleError(error, 'find users')
    }
  }

  async update(id: string, data: UpdateUserInput, updatedBy?: string): Promise<SafeUser> {
    try {
      // Get current user for audit
      const currentUser = await this.prisma.user.findUnique({
        where: { id },
      })

      if (!currentUser) {
        throw new Error('User not found')
      }

      const user = await this.prisma.user.update({
        where: { id },
        data,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          avatar: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      })

      await this.createAuditLog(
        'UPDATE',
        'User',
        user.id,
        { name: currentUser.name, role: currentUser.role, isActive: currentUser.isActive },
        data,
        updatedBy
      )

      return user
    } catch (error) {
      this.handleError(error, 'update user')
    }
  }

  async delete(id: string, deletedBy?: string): Promise<boolean> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
      })

      if (!user) {
        return false
      }

      await this.prisma.user.delete({
        where: { id },
      })

      await this.createAuditLog(
        'DELETE',
        'User',
        id,
        { email: user.email, name: user.name },
        undefined,
        deletedBy
      )

      return true
    } catch (error) {
      this.handleError(error, 'delete user')
    }
  }

  async changePassword(id: string, newPassword: string): Promise<boolean> {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 12)

      await this.prisma.account.updateMany({
        where: { 
          userId: id,
          providerId: 'credential',
        },
        data: { password: hashedPassword },
      })

      await this.createAuditLog(
        'UPDATE',
        'User',
        id,
        undefined,
        { passwordChanged: true },
        id
      )

      return true
    } catch (error) {
      this.handleError(error, 'change password')
    }
  }

  async verifyPassword(email: string, password: string): Promise<SafeUser | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { email },
        include: {
          accounts: {
            where: { providerId: 'credential' },
          },
        },
      })

      if (!user || !user.isActive || !user.accounts[0]?.password) {
        return null
      }

      const isValidPassword = await bcrypt.compare(password, user.accounts[0].password)

      if (!isValidPassword) {
        return null
      }

      // Return user without password
      const { accounts, ...safeUser } = user
      return safeUser as SafeUser
    } catch (error) {
      this.handleError(error, 'verify password')
    }
  }

  async deactivate(id: string, deactivatedBy?: string): Promise<SafeUser> {
    return this.update(id, { isActive: false }, deactivatedBy)
  }

  async activate(id: string, activatedBy?: string): Promise<SafeUser> {
    return this.update(id, { isActive: true }, activatedBy)
  }

  async findByRole(role: UserRole): Promise<SafeUser[]> {
    try {
      return await this.prisma.user.findMany({
        where: { role, isActive: true },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          avatar: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: { name: 'asc' },
      })
    } catch (error) {
      this.handleError(error, 'find users by role')
    }
  }

  async getStatistics(): Promise<{
    total: number
    active: number
    inactive: number
    byRole: Record<UserRole, number>
    recentRegistrations: number
  }> {
    try {
      const [
        total,
        active,
        inactive,
        roleCounts,
        recentRegistrations,
      ] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.user.count({ where: { isActive: true } }),
        this.prisma.user.count({ where: { isActive: false } }),
        this.prisma.user.groupBy({
          by: ['role'],
          _count: { role: true },
        }),
        this.prisma.user.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
            },
          },
        }),
      ])

      const byRole = roleCounts.reduce((acc, item) => {
        acc[item.role] = item._count.role
        return acc
      }, {} as Record<UserRole, number>)

      return {
        total,
        active,
        inactive,
        byRole,
        recentRegistrations,
      }
    } catch (error) {
      this.handleError(error, 'get user statistics')
    }
  }

  private buildUserFilters(filters: UserFilters): any {
    const where: any = {}

    if (filters.role) {
      where.role = Array.isArray(filters.role) 
        ? { in: filters.role }
        : filters.role
    }

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } },
      ]
    }

    return where
  }
}

// Export singleton instance
export const usersRepository = new UsersRepository()
