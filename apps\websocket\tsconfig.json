{"extends": "@clm/config/typescript/base.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "module": "CommonJS", "target": "ES2020", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true, "paths": {"@clm/auth/*": ["../../packages/auth/dist/*"], "@clm/db/*": ["../../packages/db/dist/*"], "@clm/lib/*": ["../../packages/lib/dist/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "../../packages"]}