import { AIService } from './ai-service';

export async function generateRiskScore(contractText: string) {
  const prompt = `Generate a risk score from 1 to 10 (1 being low risk, 10 being high risk) for the following contract, and provide recommendations to mitigate the risks.

${contractText}`;
  const result = await AIService.generateContent(prompt);
  // TODO: Parse the result to extract the score and recommendations
  return result;
}
