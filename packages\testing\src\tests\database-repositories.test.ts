import { describe, it, expect, beforeEach, vi } from 'vitest'
import { AuthRepository, UsersRepository } from '@clm/db'
import { mockUsers } from '../utils/test-helpers'

// Mock Prisma
const mockPrisma = {
  user: {
    create: vi.fn(),
    findUnique: vi.fn(),
    findMany: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    count: vi.fn(),
  },
  account: {
    create: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
}

vi.mock('@clm/db', () => ({
  prisma: mockPrisma,
  AuthRepository: vi.fn(),
  UsersRepository: vi.fn(),
}))

// Mock bcrypt
const mockBcrypt = {
  hash: vi.fn().mockResolvedValue('hashed-password'),
  compare: vi.fn().mockResolvedValue(true),
}

vi.mock('bcryptjs', () => ({
  default: mockBcrypt,
}))

describe('Database Repositories', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('AuthRepository', () => {
    let authRepo: AuthRepository

    beforeEach(() => {
      authRepo = new AuthRepository()
      // Mock the prisma property
      ;(authRepo as any).prisma = mockPrisma
    })

    describe('createUser', () => {
      it('should create user with hashed password', async () => {
        const userData = {
          email: '<EMAIL>',
          name: 'Test User',
          password: 'password123',
          role: 'USER',
        }

        const expectedUser = {
          id: 'test-user-id',
          email: userData.email,
          name: userData.name,
          role: userData.role,
          isActive: true,
          emailVerified: false,
          createdAt: new Date(),
        }

        mockPrisma.user.create.mockResolvedValue(expectedUser)

        const result = await authRepo.createUser(userData)

        expect(mockBcrypt.hash).toHaveBeenCalledWith(userData.password, 10)
        expect(mockPrisma.user.create).toHaveBeenCalledWith({
          data: {
            email: userData.email,
            name: userData.name,
            role: userData.role,
            emailVerified: false,
            accounts: {
              create: {
                accountId: userData.email,
                providerId: 'credential',
                password: 'hashed-password',
              },
            },
          },
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            isActive: true,
            emailVerified: true,
            createdAt: true,
          },
        })

        expect(result).toEqual(expectedUser)
      })

      it('should default to USER role if not specified', async () => {
        const userData = {
          email: '<EMAIL>',
          name: 'Test User',
          password: 'password123',
        }

        mockPrisma.user.create.mockResolvedValue(mockUsers.user)

        await authRepo.createUser(userData)

        expect(mockPrisma.user.create).toHaveBeenCalledWith(
          expect.objectContaining({
            data: expect.objectContaining({
              role: 'USER',
            }),
          })
        )
      })
    })

    describe('findUserByEmail', () => {
      it('should find user by email with account info', async () => {
        const userWithAccount = {
          ...mockUsers.user,
          accounts: [
            {
              password: 'hashed-password',
            },
          ],
        }

        mockPrisma.user.findUnique.mockResolvedValue(userWithAccount)

        const result = await authRepo.findUserByEmail('<EMAIL>')

        expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
          where: { email: '<EMAIL>' },
          include: {
            accounts: {
              where: { providerId: 'credential' },
              select: {
                password: true,
              },
            },
          },
        })

        expect(result).toEqual(userWithAccount)
      })

      it('should return null if user not found', async () => {
        mockPrisma.user.findUnique.mockResolvedValue(null)

        const result = await authRepo.findUserByEmail('<EMAIL>')

        expect(result).toBeNull()
      })
    })

    describe('findUserById', () => {
      it('should find user by id', async () => {
        mockPrisma.user.findUnique.mockResolvedValue(mockUsers.user)

        const result = await authRepo.findUserById('test-user-id')

        expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
          where: { id: 'test-user-id' },
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            isActive: true,
            emailVerified: true,
            avatar: true,
            createdAt: true,
            updatedAt: true,
          },
        })

        expect(result).toEqual(mockUsers.user)
      })
    })

    describe('updateUser', () => {
      it('should update user data', async () => {
        const updateData = {
          name: 'Updated Name',
          avatar: 'https://example.com/avatar.jpg',
        }

        const updatedUser = {
          ...mockUsers.user,
          ...updateData,
        }

        mockPrisma.user.update.mockResolvedValue(updatedUser)

        const result = await authRepo.updateUser('test-user-id', updateData)

        expect(mockPrisma.user.update).toHaveBeenCalledWith({
          where: { id: 'test-user-id' },
          data: updateData,
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            isActive: true,
            emailVerified: true,
            avatar: true,
            createdAt: true,
            updatedAt: true,
          },
        })

        expect(result).toEqual(updatedUser)
      })
    })

    describe('verifyPassword', () => {
      it('should verify correct password', async () => {
        const userWithAccount = {
          ...mockUsers.user,
          accounts: [
            {
              password: 'hashed-password',
            },
          ],
        }

        mockPrisma.user.findUnique.mockResolvedValue(userWithAccount)
        mockBcrypt.compare.mockResolvedValue(true)

        const result = await authRepo.verifyPassword('<EMAIL>', 'password123')

        expect(mockBcrypt.compare).toHaveBeenCalledWith('password123', 'hashed-password')
        expect(result).toBe(true)
      })

      it('should reject incorrect password', async () => {
        const userWithAccount = {
          ...mockUsers.user,
          accounts: [
            {
              password: 'hashed-password',
            },
          ],
        }

        mockPrisma.user.findUnique.mockResolvedValue(userWithAccount)
        mockBcrypt.compare.mockResolvedValue(false)

        const result = await authRepo.verifyPassword('<EMAIL>', 'wrong-password')

        expect(result).toBe(false)
      })

      it('should return false if user not found', async () => {
        mockPrisma.user.findUnique.mockResolvedValue(null)

        const result = await authRepo.verifyPassword('<EMAIL>', 'password123')

        expect(result).toBe(false)
      })

      it('should return false if user has no credential account', async () => {
        const userWithoutAccount = {
          ...mockUsers.user,
          accounts: [],
        }

        mockPrisma.user.findUnique.mockResolvedValue(userWithoutAccount)

        const result = await authRepo.verifyPassword('<EMAIL>', 'password123')

        expect(result).toBe(false)
      })
    })

    describe('getAllUsers', () => {
      it('should return paginated users with filters', async () => {
        const users = [mockUsers.admin, mockUsers.user]
        const total = 2

        mockPrisma.user.findMany.mockResolvedValue(users)
        mockPrisma.user.count.mockResolvedValue(total)

        const options = {
          limit: 10,
          offset: 0,
          search: 'admin',
          role: 'ADMIN' as const,
          isActive: true,
        }

        const result = await authRepo.getAllUsers(options)

        expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
          take: 10,
          skip: 0,
          where: {
            AND: [
              {
                OR: [
                  { name: { contains: 'admin', mode: 'insensitive' } },
                  { email: { contains: 'admin', mode: 'insensitive' } },
                ],
              },
              { role: 'ADMIN' },
              { isActive: true },
            ],
          },
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            isActive: true,
            emailVerified: true,
            avatar: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        })

        expect(mockPrisma.user.count).toHaveBeenCalledWith({
          where: {
            AND: [
              {
                OR: [
                  { name: { contains: 'admin', mode: 'insensitive' } },
                  { email: { contains: 'admin', mode: 'insensitive' } },
                ],
              },
              { role: 'ADMIN' },
              { isActive: true },
            ],
          },
        })

        expect(result).toEqual({
          users,
          total,
          hasMore: false,
        })
      })

      it('should handle empty filters', async () => {
        const users = [mockUsers.admin, mockUsers.user]
        const total = 2

        mockPrisma.user.findMany.mockResolvedValue(users)
        mockPrisma.user.count.mockResolvedValue(total)

        const result = await authRepo.getAllUsers({})

        expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
          take: 10,
          skip: 0,
          where: {},
          select: expect.any(Object),
          orderBy: {
            createdAt: 'desc',
          },
        })

        expect(result.users).toEqual(users)
        expect(result.total).toBe(total)
      })
    })
  })
})
