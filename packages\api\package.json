{"name": "@clm/api", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@clm/db": "*", "@trpc/server": "^10.45.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "superjson": "^2.2.1", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.14.10", "eslint": "^8.57.0", "typescript": "^5.5.3"}}