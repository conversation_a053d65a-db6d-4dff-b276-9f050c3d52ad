{"name": "@clm/testing", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "lint": "eslint src/", "type-check": "tsc --noEmit"}, "dependencies": {"@clm/auth": "*", "@clm/db": "*", "@clm/lib": "*", "@clm/api": "*", "@testing-library/react": "^14.3.1", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "@testing-library/jest-dom": "^6.4.6", "msw": "^2.3.1", "prisma": "^5.16.1", "@prisma/client": "^5.16.1"}, "devDependencies": {"@types/node": "^20.14.10", "@vitest/coverage-v8": "^1.6.0", "@vitest/ui": "^1.6.0", "eslint": "^8.57.0", "jsdom": "^24.1.0", "typescript": "^5.5.3", "vitest": "^1.6.0"}}