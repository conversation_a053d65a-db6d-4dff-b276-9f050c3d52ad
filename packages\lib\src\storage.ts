import { generateFileName, getFileExtension } from './file'

export interface StorageConfig {
  endpoint: string
  accessKey: string
  secretKey: string
  bucketName: string
  useSSL: boolean
  region?: string
}

export interface UploadOptions {
  fileName?: string
  contentType?: string
  metadata?: Record<string, string>
  isPublic?: boolean
  prefix?: string
}

export interface FileMetadata {
  fileName: string
  originalName: string
  size: number
  contentType: string
  url: string
  key: string
  uploadedAt: Date
  metadata?: Record<string, string>
}

// MinIO/S3 compatible storage client simulation
class StorageClient {
  private files = new Map<string, {
    data: Buffer
    metadata: FileMetadata
  }>()

  constructor(private config: StorageConfig) {}

  async uploadFile(
    file: Buffer | Uint8Array,
    originalName: string,
    options?: UploadOptions
  ): Promise<FileMetadata> {
    const fileName = options?.fileName || generateFileName(originalName, options?.prefix)
    const key = `${options?.prefix ? options.prefix + '/' : ''}${fileName}`
    const contentType = options?.contentType || this.getContentType(originalName)
    
    const metadata: FileMetadata = {
      fileName,
      originalName,
      size: file.length,
      contentType,
      url: this.generateUrl(key, options?.isPublic),
      key,
      uploadedAt: new Date(),
      metadata: options?.metadata,
    }

    this.files.set(key, {
      data: Buffer.from(file),
      metadata,
    })

    return metadata
  }

  async downloadFile(key: string): Promise<Buffer | null> {
    const file = this.files.get(key)
    return file ? file.data : null
  }

  async deleteFile(key: string): Promise<boolean> {
    return this.files.delete(key)
  }

  async getFileMetadata(key: string): Promise<FileMetadata | null> {
    const file = this.files.get(key)
    return file ? file.metadata : null
  }

  async listFiles(prefix?: string): Promise<FileMetadata[]> {
    const files: FileMetadata[] = []
    
    for (const [key, file] of this.files.entries()) {
      if (!prefix || key.startsWith(prefix)) {
        files.push(file.metadata)
      }
    }
    
    return files.sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime())
  }

  async fileExists(key: string): Promise<boolean> {
    return this.files.has(key)
  }

  generateSignedUrl(key: string, expiresIn = 3600): string {
    // In real implementation, this would generate a signed URL
    const baseUrl = `${this.config.useSSL ? 'https' : 'http'}://${this.config.endpoint}`
    const timestamp = Date.now() + (expiresIn * 1000)
    return `${baseUrl}/${this.config.bucketName}/${key}?expires=${timestamp}&signature=mock`
  }

  private generateUrl(key: string, isPublic = false): string {
    const baseUrl = `${this.config.useSSL ? 'https' : 'http'}://${this.config.endpoint}`
    
    if (isPublic) {
      return `${baseUrl}/${this.config.bucketName}/${key}`
    }
    
    return this.generateSignedUrl(key)
  }

  private getContentType(fileName: string): string {
    const extension = getFileExtension(fileName).toLowerCase()
    
    const mimeTypes: Record<string, string> = {
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      txt: 'text/plain',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
    }
    
    return mimeTypes[extension] || 'application/octet-stream'
  }

  // Bucket management
  async createBucket(): Promise<boolean> {
    // In real implementation, this would create the bucket
    console.log(`Bucket ${this.config.bucketName} created (simulated)`)
    return true
  }

  async bucketExists(): Promise<boolean> {
    // In real implementation, this would check if bucket exists
    return true
  }

  async setBucketPolicy(policy: any): Promise<boolean> {
    // In real implementation, this would set bucket policy
    console.log('Bucket policy set (simulated):', policy)
    return true
  }
}

// File upload utilities
export class FileUploadManager {
  constructor(private storage: StorageClient) {}

  async uploadContractDocument(
    file: Buffer,
    originalName: string,
    contractId: string,
    userId: string
  ): Promise<FileMetadata> {
    return this.storage.uploadFile(file, originalName, {
      prefix: `contracts/${contractId}`,
      metadata: {
        contractId,
        uploadedBy: userId,
        category: 'contract-document',
      },
    })
  }

  async uploadUserAvatar(
    file: Buffer,
    originalName: string,
    userId: string
  ): Promise<FileMetadata> {
    return this.storage.uploadFile(file, originalName, {
      prefix: `avatars`,
      fileName: `${userId}.${getFileExtension(originalName)}`,
      isPublic: true,
      metadata: {
        userId,
        category: 'avatar',
      },
    })
  }

  async uploadTemplate(
    file: Buffer,
    originalName: string,
    templateId: string,
    userId: string
  ): Promise<FileMetadata> {
    return this.storage.uploadFile(file, originalName, {
      prefix: `templates/${templateId}`,
      metadata: {
        templateId,
        uploadedBy: userId,
        category: 'template',
      },
    })
  }

  async getContractDocuments(contractId: string): Promise<FileMetadata[]> {
    return this.storage.listFiles(`contracts/${contractId}`)
  }

  async deleteContractDocument(contractId: string, fileName: string): Promise<boolean> {
    const key = `contracts/${contractId}/${fileName}`
    return this.storage.deleteFile(key)
  }

  async generateDownloadUrl(key: string, expiresIn = 3600): Promise<string> {
    return this.storage.generateSignedUrl(key, expiresIn)
  }
}

// Storage configuration
export function createStorageConfig(): StorageConfig {
  return {
    endpoint: process.env.MINIO_ENDPOINT || 'localhost:9000',
    accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
    secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin',
    bucketName: process.env.MINIO_BUCKET_NAME || 'clm-documents',
    useSSL: process.env.MINIO_USE_SSL === 'true',
    region: process.env.MINIO_REGION || 'us-east-1',
  }
}

// Default bucket policy for CLM documents
export const defaultBucketPolicy = {
  Version: '2012-10-17',
  Statement: [
    {
      Effect: 'Allow',
      Principal: { AWS: ['*'] },
      Action: ['s3:GetObject'],
      Resource: ['arn:aws:s3:::clm-documents/public/*'],
    },
    {
      Effect: 'Deny',
      Principal: { AWS: ['*'] },
      Action: ['s3:GetObject'],
      Resource: ['arn:aws:s3:::clm-documents/contracts/*'],
      Condition: {
        StringNotEquals: {
          's3:ExistingObjectTag/access': 'public',
        },
      },
    },
  ],
}

// Export singleton instances
const storageConfig = createStorageConfig()
export const storageClient = new StorageClient(storageConfig)
export const fileUploadManager = new FileUploadManager(storageClient)

// Utility functions
export function validateFileForUpload(file: {
  name: string
  size: number
  type: string
}): { isValid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024 // 10MB
  const allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png']
  
  if (file.size > maxSize) {
    return { isValid: false, error: 'File size exceeds 10MB limit' }
  }
  
  const extension = getFileExtension(file.name).toLowerCase()
  if (!allowedTypes.includes(extension)) {
    return { isValid: false, error: 'File type not allowed' }
  }
  
  return { isValid: true }
}

export function generateSecureFileName(originalName: string, userId: string): string {
  const extension = getFileExtension(originalName)
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `${userId}_${timestamp}_${random}.${extension}`
}
