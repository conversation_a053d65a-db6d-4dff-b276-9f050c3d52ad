import { useState } from 'react'
import { trpc } from '@/lib/trpc'
import { Button, Input, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@clm/ui'
import Link from 'next/link'

export default function UsersManagementPage() {
  const [search, setSearch] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('')
  const [statusFilter, setStatusFilter] = useState<string>('')

  const { data: users, isLoading, refetch } = trpc.auth.getAllUsers.useQuery({
    limit: 20,
    search: search || undefined,
    role: (roleFilter as "USER" | "ADMIN" | "LEGAL" | "FINANCE" | "VIEWER") || undefined,
    isActive: statusFilter === 'active' ? true : statusFilter === 'inactive' ? false : undefined,
  })

  const deactivateUserMutation = trpc.auth.deactivateUser.useMutation({
    onSuccess: () => {
      refetch()
    },
  })

  const activateUserMutation = trpc.auth.activateUser.useMutation({
    onSuccess: () => {
      refetch()
    },
  })

  const deleteUserMutation = trpc.auth.deleteUser.useMutation({
    onSuccess: () => {
      refetch()
    },
  })

  const handleDeactivateUser = async (userId: string) => {
    if (confirm('Are you sure you want to deactivate this user?')) {
      await deactivateUserMutation.mutateAsync({ userId })
    }
  }

  const handleActivateUser = async (userId: string) => {
    await activateUserMutation.mutateAsync({ userId })
  }

  const handleDeleteUser = async (userId: string) => {
    if (confirm('Are you sure you want to permanently delete this user? This action cannot be undone.')) {
      await deleteUserMutation.mutateAsync({ userId })
    }
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-red-100 text-red-800'
      case 'LEGAL': return 'bg-blue-100 text-blue-800'
      case 'FINANCE': return 'bg-green-100 text-green-800'
      case 'USER': return 'bg-gray-100 text-gray-800'
      case 'VIEWER': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link href="/admin" className="text-blue-600 hover:text-blue-800 text-sm">
                ← Back to Admin Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mt-2">User Management</h1>
              <p className="text-gray-600">Manage all system users and their permissions</p>
            </div>
            <Link href="/admin/users/create">
              <Button>
                👤 Create New User
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search users by name or email..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="w-full sm:w-48">
                <select
                  value={roleFilter}
                  onChange={(e) => setRoleFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Roles</option>
                  <option value="ADMIN">Admin</option>
                  <option value="LEGAL">Legal</option>
                  <option value="FINANCE">Finance</option>
                  <option value="USER">User</option>
                  <option value="VIEWER">Viewer</option>
                </select>
              </div>
              <div className="w-full sm:w-48">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <Button variant="outline" onClick={() => refetch()}>
                🔄 Refresh
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Users List */}
        <div className="space-y-4">
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="pt-6">
                    <div className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                      <div className="flex space-x-4">
                        <div className="h-3 bg-gray-200 rounded w-20"></div>
                        <div className="h-3 bg-gray-200 rounded w-24"></div>
                        <div className="h-3 bg-gray-200 rounded w-16"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : users?.users.length ? (
            users.users.map((user: any) => (
              <Card key={user.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {user.name}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${getRoleBadgeColor(user.role)}`}>
                          {user.role}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {user.isActive ? 'Active' : 'Inactive'}
                        </span>
                        {user.emailVerified && (
                          <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                            ✓ Verified
                          </span>
                        )}
                      </div>
                      
                      <p className="text-gray-600 mb-3">
                        {user.email}
                      </p>
                      
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                        <span className="flex items-center">
                          📅 Joined {new Date(user.createdAt).toLocaleDateString('id-ID')}
                        </span>
                        <span className="flex items-center">
                          📝 {user._count.contracts} contracts
                        </span>
                        <span className="flex items-center">
                          💬 {user._count.comments} comments
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-2 ml-4">
                      <Link href={`/admin/users/${user.id}`}>
                        <Button variant="outline" size="sm" className="w-full">
                          👁️ View
                        </Button>
                      </Link>
                      <Link href={`/admin/users/${user.id}/edit`}>
                        <Button variant="outline" size="sm" className="w-full">
                          ✏️ Edit
                        </Button>
                      </Link>
                      {user.isActive ? (
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full text-orange-600 hover:text-orange-700"
                          onClick={() => handleDeactivateUser(user.id)}
                          disabled={deactivateUserMutation.isLoading}
                        >
                          🚫 Deactivate
                        </Button>
                      ) : (
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full text-green-600 hover:text-green-700"
                          onClick={() => handleActivateUser(user.id)}
                          disabled={activateUserMutation.isLoading}
                        >
                          ✅ Activate
                        </Button>
                      )}
                      {user.role !== 'ADMIN' && (
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full text-red-600 hover:text-red-700"
                          onClick={() => handleDeleteUser(user.id)}
                          disabled={deleteUserMutation.isLoading}
                        >
                          🗑️ Delete
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">👥</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No users found
                  </h3>
                  <p className="text-gray-500 mb-6">
                    {search || roleFilter || statusFilter 
                      ? 'No users match your current filters.'
                      : 'No users have been created yet.'
                    }
                  </p>
                  {!search && !roleFilter && !statusFilter && (
                    <Link href="/admin/users/create">
                      <Button>
                        👤 Create First User
                      </Button>
                    </Link>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Pagination */}
        {users?.hasMore && (
          <div className="flex justify-center mt-6">
            <Button variant="outline">
              Load More Users
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
