import { opensearchClient } from './opensearch';
import { createReadStream, readFileSync } from 'fs';
import { resolve } from 'path';

export async function indexDocument(filePath: string, documentId: string) {
  const file = readFileSync(filePath, 'base64');

  await opensearchClient.index({
    index: 'contracts',
    id: documentId,
    body: {
      data: file,
    },
    pipeline: 'contract-pipeline',
  });
}
