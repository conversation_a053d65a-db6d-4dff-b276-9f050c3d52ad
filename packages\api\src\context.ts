import { type CreateNextContextOptions } from '@trpc/server/adapters/next'
import { type Context } from './types/context'

export async function createTRPCContext({ req, res }: CreateNextContextOptions): Promise<Context> {
  // TODO: Implement session management with BetterAuth
  // For now, return null session to avoid build errors
  return {
    session: null,
    req: req as unknown as Request,
    res: res as unknown as Response,
  }
}

export type TRPCContext = Context