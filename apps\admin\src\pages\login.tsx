import { useEffect } from 'react'
import { useRouter } from 'next/router'
import { useAuth, SignInForm } from '@clm/auth'

export default function LoginPage() {
  const router = useRouter()
  const { user, isLoading } = useAuth()

  useEffect(() => {
    if (!isLoading && user) {
      if (user.role === 'ADMIN') {
        router.push('/admin')
      } else {
        router.push('/unauthorized')
      }
    }
  }, [user, isLoading, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (user) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">CLM Admin Panel</h1>
          <p className="mt-2 text-gray-600">Developer & Administrator Access Only</p>
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              ⚠️ This is a restricted area. Only administrators can access this panel.
            </p>
          </div>
        </div>

        <SignInForm 
          redirectTo="/admin" 
          showDemoAccounts={false}
          onSuccess={() => {
            // Will redirect to /admin automatically
            // Admin role check will be handled by middleware
          }}
        />
      </div>
    </div>
  )
}
