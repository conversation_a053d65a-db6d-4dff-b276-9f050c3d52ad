import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, mockUsers, mockContracts } from '@clm/testing'
import ContractsPage from '../app/dashboard/contracts/page'

// Mock tRPC
const mockRefetch = vi.fn()
const mockTrpcData = {
  contracts: [mockContracts.active, mockContracts.draft],
  nextCursor: null,
}

vi.mock('@/lib/trpc', () => ({
  trpc: {
    contracts: {
      getAll: {
        useQuery: vi.fn(() => ({
          data: mockTrpcData,
          isLoading: false,
          refetch: mockRefetch,
        })),
      },
    },
  },
}))

// Mock permissions hook
const mockPermissions = {
  canCreateContracts: vi.fn(() => true),
  canEditContract: vi.fn(() => true),
  canDeleteContract: vi.fn(() => true),
  canViewContracts: vi.fn(() => true),
}

vi.mock('@clm/auth', () => ({
  usePermissions: () => mockPermissions,
}))

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: '',
  },
  writable: true,
})

describe('Contracts Page', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    window.location.href = ''
  })

  it('should render contracts page with header', () => {
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Kontrak')
    expect(screen.getByText('Kelola semua kontrak Anda')).toBeInTheDocument()
  })

  it('should show create contract button for users with permission', () => {
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    const createButton = screen.getByRole('button', { name: /buat kontrak baru/i })
    expect(createButton).toBeInTheDocument()
  })

  it('should hide create contract button for users without permission', () => {
    mockPermissions.canCreateContracts.mockReturnValue(false)
    
    renderWithProviders(<ContractsPage />, { user: mockUsers.viewer })

    expect(screen.queryByRole('button', { name: /buat kontrak baru/i })).not.toBeInTheDocument()
  })

  it('should render filter controls', () => {
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    // Search input
    expect(screen.getByPlaceholderText('Cari kontrak...')).toBeInTheDocument()

    // Status filter dropdown
    const statusFilter = screen.getByDisplayValue('Semua Status')
    expect(statusFilter).toBeInTheDocument()

    // Refresh button
    expect(screen.getByRole('button', { name: /refresh/i })).toBeInTheDocument()
  })

  it('should display contracts list', () => {
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    // Check for contract titles
    expect(screen.getByText('Service Agreement Q1 2024')).toBeInTheDocument()
    expect(screen.getByText('NDA Agreement - Tech Partnership')).toBeInTheDocument()

    // Check for contract statuses
    expect(screen.getByText('ACTIVE')).toBeInTheDocument()
    expect(screen.getByText('DRAFT')).toBeInTheDocument()
  })

  it('should handle search functionality', async () => {
    const user = userEvent.setup()
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    const searchInput = screen.getByPlaceholderText('Cari kontrak...')
    await user.type(searchInput, 'Service Agreement')

    expect(searchInput).toHaveValue('Service Agreement')
  })

  it('should handle status filter', async () => {
    const user = userEvent.setup()
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    const statusFilter = screen.getByDisplayValue('Semua Status')
    await user.selectOptions(statusFilter, 'ACTIVE')

    expect(statusFilter).toHaveValue('ACTIVE')
  })

  it('should handle refresh button click', async () => {
    const user = userEvent.setup()
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    const refreshButton = screen.getByRole('button', { name: /refresh/i })
    await user.click(refreshButton)

    expect(mockRefetch).toHaveBeenCalled()
  })

  it('should navigate to create contract page', async () => {
    const user = userEvent.setup()
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    const createButton = screen.getByRole('button', { name: /buat kontrak baru/i })
    await user.click(createButton)

    expect(window.location.href).toBe('/dashboard/contracts/new')
  })

  it('should display contract actions based on permissions', () => {
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    // Should show view, edit, and delete buttons
    const viewButtons = screen.getAllByRole('button', { name: /lihat/i })
    const editButtons = screen.getAllByRole('button', { name: /edit/i })
    const deleteButtons = screen.getAllByRole('button', { name: /hapus/i })

    expect(viewButtons.length).toBeGreaterThan(0)
    expect(editButtons.length).toBeGreaterThan(0)
    expect(deleteButtons.length).toBeGreaterThan(0)
  })

  it('should handle contract view navigation', async () => {
    const user = userEvent.setup()
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    const viewButton = screen.getAllByRole('button', { name: /lihat/i })[0]
    await user.click(viewButton)

    expect(window.location.href).toContain('/dashboard/contracts/')
  })

  it('should display contract metadata correctly', () => {
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    // Check for creator names
    expect(screen.getByText('Admin CLM')).toBeInTheDocument()
    expect(screen.getByText('Regular User')).toBeInTheDocument()

    // Check for formatted currency
    expect(screen.getByText(/Rp50\.000\.000/)).toBeInTheDocument()

    // Check for comment and version counts
    expect(screen.getByText(/komentar/)).toBeInTheDocument()
    expect(screen.getByText(/versi/)).toBeInTheDocument()
  })

  it('should show empty state when no contracts', () => {
    vi.mocked(require('@/lib/trpc').trpc.contracts.getAll.useQuery).mockReturnValue({
      data: { contracts: [], nextCursor: null },
      isLoading: false,
      refetch: mockRefetch,
    })

    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    expect(screen.getByText('Belum ada kontrak')).toBeInTheDocument()
    expect(screen.getByText(/mulai dengan membuat kontrak pertama/i)).toBeInTheDocument()
  })

  it('should show loading state', () => {
    vi.mocked(require('@/lib/trpc').trpc.contracts.getAll.useQuery).mockReturnValue({
      data: undefined,
      isLoading: true,
      refetch: mockRefetch,
    })

    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    // Should show loading skeletons
    const loadingElements = screen.getAllByText('...') // Loading text in skeletons
    expect(loadingElements.length).toBeGreaterThan(0)
  })

  it('should handle pagination', () => {
    const dataWithPagination = {
      contracts: [mockContracts.active, mockContracts.draft],
      nextCursor: 'next-page-cursor',
    }

    vi.mocked(require('@/lib/trpc').trpc.contracts.getAll.useQuery).mockReturnValue({
      data: dataWithPagination,
      isLoading: false,
      refetch: mockRefetch,
    })

    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    expect(screen.getByRole('button', { name: /muat lebih banyak/i })).toBeInTheDocument()
  })

  it('should display contract parties information', () => {
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    // Check for parties section
    expect(screen.getByText('Pihak terlibat:')).toBeInTheDocument()
    expect(screen.getByText('PT. Client Indonesia (CLIENT)')).toBeInTheDocument()
  })

  it('should format dates correctly', () => {
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    // Check for Indonesian date format
    const dateElements = screen.getAllByText(/\/2024|2024/)
    expect(dateElements.length).toBeGreaterThan(0)
  })

  it('should be accessible', () => {
    renderWithProviders(<ContractsPage />, { user: mockUsers.admin })

    // Check for proper heading hierarchy
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument()

    // Check for proper form labels
    expect(screen.getByLabelText(/cari kontrak/i)).toBeInTheDocument()

    // Check for button accessibility
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button).toBeInTheDocument()
    })
  })
})
