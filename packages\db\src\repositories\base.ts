import { PrismaClient, Prisma } from '@prisma/client'
import { prisma } from '../index'

export interface PaginationOptions {
  page?: number
  limit?: number
  cursor?: string
}

export interface PaginationResult<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
    nextCursor?: string
  }
}

export interface SortOptions {
  field: string
  direction: 'asc' | 'desc'
}

export interface FilterOptions {
  [key: string]: any
}

export abstract class BaseRepository<T, CreateInput, UpdateInput> {
  protected prisma: PrismaClient

  constructor() {
    this.prisma = prisma
  }

  // Abstract methods to be implemented by concrete repositories
  abstract create(data: CreateInput): Promise<T>
  abstract findById(id: string): Promise<T | null>
  abstract update(id: string, data: UpdateInput): Promise<T>
  abstract delete(id: string): Promise<boolean>

  // Common transaction wrapper
  async withTransaction<R>(
    fn: (tx: Prisma.TransactionClient) => Promise<R>
  ): Promise<R> {
    return this.prisma.$transaction(fn)
  }

  // Common error handling
  protected handleError(error: any, operation: string): never {
    console.error(`Repository error in ${operation}:`, error)
    
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          throw new Error('Unique constraint violation')
        case 'P2025':
          throw new Error('Record not found')
        case 'P2003':
          throw new Error('Foreign key constraint violation')
        default:
          throw new Error(`Database error: ${error.message}`)
      }
    }
    
    if (error instanceof Prisma.PrismaClientValidationError) {
      throw new Error('Invalid data provided')
    }
    
    throw new Error(`Unexpected error in ${operation}: ${error.message}`)
  }

  // Common pagination helper
  protected async paginate<Model>(
    model: Prisma.ModelName,
    query: any,
    options: PaginationOptions = {}
  ): Promise<PaginationResult<Model>> {
    const page = options.page || 1
    const limit = Math.min(options.limit || 10, 100) // Max 100 items per page
    const skip = (page - 1) * limit

    try {
      const [data, total] = await Promise.all([
        (this.prisma as any)[model].findMany(query),
        (this.prisma as any)[model].count({ where: query.where }),
      ])

      const totalPages = Math.ceil(total / limit)
      const hasNext = page < totalPages
      const hasPrev = page > 1

      return {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext,
          hasPrev,
          nextCursor: hasNext && data.length > 0 ? data[data.length - 1].id : undefined,
        },
      }
    } catch (error) {
      this.handleError(error, 'pagination')
    }
  }

  // Common search helper
  protected buildSearchQuery(
    searchTerm: string,
    searchFields: string[]
  ): Prisma.StringFilter[] {
    if (!searchTerm) return []

    return searchFields.map(field => ({
      [field]: {
        contains: searchTerm,
        mode: 'insensitive' as const,
      },
    }))
  }

  // Common filter builder
  protected buildFilters(filters: FilterOptions): any {
    const where: any = {}

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          where[key] = { in: value }
        } else if (typeof value === 'object' && value.from && value.to) {
          // Date range filter
          where[key] = {
            gte: value.from,
            lte: value.to,
          }
        } else {
          where[key] = value
        }
      }
    })

    return where
  }

  // Common sort builder
  protected buildSort(sort?: SortOptions): any {
    if (!sort) return { createdAt: 'desc' }

    return {
      [sort.field]: sort.direction,
    }
  }

  // Audit logging helper
  protected async createAuditLog(
    action: string,
    entity: string,
    entityId: string,
    oldValues?: any,
    newValues?: any,
    userId?: string
  ): Promise<void> {
    try {
      await this.prisma.auditLog.create({
        data: {
          action,
          entity,
          entityId,
          oldValues: oldValues ? JSON.stringify(oldValues) : undefined,
          newValues: newValues ? JSON.stringify(newValues) : undefined,
          userId,
        },
      })
    } catch (error) {
      // Don't fail the main operation if audit logging fails
      console.error('Failed to create audit log:', error)
    }
  }

  // Soft delete helper (if using soft deletes)
  protected async softDelete(model: string, id: string): Promise<boolean> {
    try {
      await (this.prisma as any)[model].update({
        where: { id },
        data: { deletedAt: new Date() },
      })
      return true
    } catch (error) {
      this.handleError(error, 'soft delete')
    }
  }

  // Bulk operations helper
  protected async bulkCreate<Model>(
    model: string,
    data: any[]
  ): Promise<Prisma.BatchPayload> {
    try {
      return await (this.prisma as any)[model].createMany({
        data,
        skipDuplicates: true,
      })
    } catch (error) {
      this.handleError(error, 'bulk create')
    }
  }

  protected async bulkUpdate<Model>(
    model: string,
    where: any,
    data: any
  ): Promise<Prisma.BatchPayload> {
    try {
      return await (this.prisma as any)[model].updateMany({
        where,
        data,
      })
    } catch (error) {
      this.handleError(error, 'bulk update')
    }
  }

  protected async bulkDelete<Model>(
    model: string,
    where: any
  ): Promise<Prisma.BatchPayload> {
    try {
      return await (this.prisma as any)[model].deleteMany({
        where,
      })
    } catch (error) {
      this.handleError(error, 'bulk delete')
    }
  }

  // Connection health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`
      return true
    } catch (error) {
      console.error('Database health check failed:', error)
      return false
    }
  }
}
