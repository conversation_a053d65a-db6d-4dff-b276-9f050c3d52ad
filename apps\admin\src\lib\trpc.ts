import { createTRPCNext } from '@trpc/next'
import { httpBatchLink } from '@trpc/client'
import { AppRouter } from '@clm/api'
import superjson from 'superjson'

function getBaseUrl() {
  if (typeof window !== 'undefined') return '' // browser should use relative url
  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}` // SSR should use vercel url
  return `http://localhost:${process.env.PORT ?? 3001}` // dev SSR should use localhost
}

export const trpc = createTRPCNext<AppRouter>({
  config() {
    return {
      transformer: superjson,
      links: [
        httpBatchLink({
          url: `${getBaseUrl()}/api/trpc`,
          headers() {
            // Get auth token from Better Auth
            if (typeof window !== 'undefined') {
              const token = localStorage.getItem('better-auth.session-token')
              if (token) {
                return {
                  authorization: `Bearer ${token}`,
                }
              }
            }
            return {}
          },
        }),
      ],
    }
  },
  ssr: false,
})
