import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { usePathname } from 'next/navigation'
import { Sidebar } from '../components/layout/sidebar'
import { mockUsers, renderWithProviders } from '@clm/testing'

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  usePathname: vi.fn(),
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock auth functions
const mockSignOut = vi.fn()
vi.mock('@clm/auth', () => ({
  useAuth: () => ({
    user: mockUsers.admin,
  }),
  signOut: mockSignOut,
}))

describe('Sidebar', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(usePathname).mockReturnValue('/dashboard')
  })

  it('should render CLM Platform logo', () => {
    render(renderWithProviders(<Sidebar />, { user: mockUsers.admin }).container)
    
    expect(screen.getByText('CLM Platform')).toBeInTheDocument()
  })

  it('should display user information', () => {
    render(renderWithProviders(<Sidebar />, { user: mockUsers.admin }).container)
    
    expect(screen.getByText('Admin CLM')).toBeInTheDocument()
    expect(screen.getByText('ADMIN')).toBeInTheDocument()
    expect(screen.getByText('A')).toBeInTheDocument() // Avatar initial
  })

  it('should render all navigation items', () => {
    render(renderWithProviders(<Sidebar />, { user: mockUsers.admin }).container)
    
    const expectedNavItems = [
      'Dashboard',
      'Kontrak',
      'Template',
      'Workflow',
      'Notifikasi',
      'Pengaturan'
    ]

    expectedNavItems.forEach(item => {
      expect(screen.getByText(item)).toBeInTheDocument()
    })
  })

  it('should highlight active navigation item', () => {
    vi.mocked(usePathname).mockReturnValue('/dashboard/contracts')
    
    render(renderWithProviders(<Sidebar />, { user: mockUsers.admin }).container)
    
    const contractsLink = screen.getByText('Kontrak').closest('a')
    expect(contractsLink).toHaveClass('bg-blue-50', 'text-blue-700')
  })

  it('should not highlight inactive navigation items', () => {
    vi.mocked(usePathname).mockReturnValue('/dashboard')
    
    render(renderWithProviders(<Sidebar />, { user: mockUsers.admin }).container)
    
    const contractsLink = screen.getByText('Kontrak').closest('a')
    expect(contractsLink).toHaveClass('text-gray-600')
    expect(contractsLink).not.toHaveClass('bg-blue-50', 'text-blue-700')
  })

  it('should render logout button', () => {
    render(renderWithProviders(<Sidebar />, { user: mockUsers.admin }).container)
    
    expect(screen.getByText('Keluar')).toBeInTheDocument()
  })

  it('should call signOut when logout button is clicked', async () => {
    render(renderWithProviders(<Sidebar />, { user: mockUsers.admin }).container)
    
    const logoutButton = screen.getByText('Keluar')
    fireEvent.click(logoutButton)
    
    await waitFor(() => {
      expect(mockSignOut).toHaveBeenCalledTimes(1)
    })
  })

  it('should display correct navigation icons', () => {
    render(renderWithProviders(<Sidebar />, { user: mockUsers.admin }).container)
    
    const expectedIcons = ['📊', '📄', '📝', '🔄', '🔔', '⚙️']
    
    expectedIcons.forEach(icon => {
      expect(screen.getByText(icon)).toBeInTheDocument()
    })
  })

  it('should render user avatar with correct initial', () => {
    const userWithDifferentName = { ...mockUsers.admin, name: 'John Doe' }
    
    render(renderWithProviders(<Sidebar />, { user: userWithDifferentName }).container)
    
    expect(screen.getByText('J')).toBeInTheDocument()
  })

  it('should handle user with no name gracefully', () => {
    const userWithoutName = { ...mockUsers.admin, name: '' }
    
    render(renderWithProviders(<Sidebar />, { user: userWithoutName }).container)
    
    // Should not crash and should render empty or default state
    expect(screen.getByText('CLM Platform')).toBeInTheDocument()
  })

  it('should have correct href attributes for navigation links', () => {
    render(renderWithProviders(<Sidebar />, { user: mockUsers.admin }).container)
    
    const dashboardLink = screen.getByText('Dashboard').closest('a')
    const contractsLink = screen.getByText('Kontrak').closest('a')
    const templatesLink = screen.getByText('Template').closest('a')
    
    expect(dashboardLink).toHaveAttribute('href', '/dashboard')
    expect(contractsLink).toHaveAttribute('href', '/dashboard/contracts')
    expect(templatesLink).toHaveAttribute('href', '/dashboard/templates')
  })
})
