// Redis utilities and configuration
// Note: In production, use actual Redis client like ioredis

export interface RedisConfig {
  url: string
  maxRetries: number
  retryDelayOnFailover: number
  enableReadyCheck: boolean
  maxRetriesPerRequest: number
}

export interface CacheOptions {
  ttl?: number // Time to live in seconds
  prefix?: string
}

export interface QueueJob {
  id: string
  type: string
  data: any
  priority: number
  attempts: number
  maxAttempts: number
  createdAt: number
  processAt: number
}

// In-memory Redis simulation for development
class RedisClient {
  private data = new Map<string, { value: any; expiry?: number }>()
  private queues = new Map<string, QueueJob[]>()
  private subscribers = new Map<string, Set<(message: any) => void>>()

  // Basic Redis operations
  async get(key: string): Promise<string | null> {
    const item = this.data.get(key)
    if (!item) return null
    
    if (item.expiry && Date.now() > item.expiry) {
      this.data.delete(key)
      return null
    }
    
    return typeof item.value === 'string' ? item.value : JSON.stringify(item.value)
  }

  async set(key: string, value: any, options?: { EX?: number }): Promise<void> {
    const expiry = options?.EX ? Date.now() + (options.EX * 1000) : undefined
    this.data.set(key, { value, expiry })
  }

  async del(key: string): Promise<number> {
    return this.data.delete(key) ? 1 : 0
  }

  async exists(key: string): Promise<number> {
    const item = this.data.get(key)
    if (!item) return 0
    
    if (item.expiry && Date.now() > item.expiry) {
      this.data.delete(key)
      return 0
    }
    
    return 1
  }

  async expire(key: string, seconds: number): Promise<number> {
    const item = this.data.get(key)
    if (!item) return 0
    
    item.expiry = Date.now() + (seconds * 1000)
    return 1
  }

  async ttl(key: string): Promise<number> {
    const item = this.data.get(key)
    if (!item) return -2
    
    if (!item.expiry) return -1
    
    const remaining = Math.ceil((item.expiry - Date.now()) / 1000)
    return remaining > 0 ? remaining : -2
  }

  // Hash operations
  async hget(key: string, field: string): Promise<string | null> {
    const hash = await this.get(key)
    if (!hash) return null
    
    try {
      const parsed = JSON.parse(hash)
      return parsed[field] || null
    } catch {
      return null
    }
  }

  async hset(key: string, field: string, value: any): Promise<number> {
    const existing = await this.get(key)
    let hash: Record<string, any> = {}
    
    if (existing) {
      try {
        hash = JSON.parse(existing)
      } catch {
        // Invalid JSON, start fresh
      }
    }
    
    const isNew = !(field in hash)
    hash[field] = value
    await this.set(key, hash)
    return isNew ? 1 : 0
  }

  async hdel(key: string, field: string): Promise<number> {
    const existing = await this.get(key)
    if (!existing) return 0
    
    try {
      const hash = JSON.parse(existing)
      if (!(field in hash)) return 0
      
      delete hash[field]
      await this.set(key, hash)
      return 1
    } catch {
      return 0
    }
  }

  // List operations for queues
  async lpush(key: string, ...values: any[]): Promise<number> {
    let queue = this.queues.get(key) || []
    queue.unshift(...values)
    this.queues.set(key, queue)
    return queue.length
  }

  async rpop(key: string): Promise<any> {
    const queue = this.queues.get(key)
    if (!queue || queue.length === 0) return null
    
    const item = queue.pop()
    if (queue.length === 0) {
      this.queues.delete(key)
    }
    return item
  }

  async llen(key: string): Promise<number> {
    const queue = this.queues.get(key)
    return queue ? queue.length : 0
  }

  // Pub/Sub operations
  async publish(channel: string, message: any): Promise<number> {
    const subscribers = this.subscribers.get(channel)
    if (!subscribers) return 0
    
    const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
    subscribers.forEach(callback => {
      try {
        callback(messageStr)
      } catch (error) {
        console.error('Error in subscriber callback:', error)
      }
    })
    
    return subscribers.size
  }

  async subscribe(channel: string, callback: (message: any) => void): Promise<void> {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, new Set())
    }
    this.subscribers.get(channel)!.add(callback)
  }

  async unsubscribe(channel: string, callback: (message: any) => void): Promise<void> {
    const subscribers = this.subscribers.get(channel)
    if (subscribers) {
      subscribers.delete(callback)
      if (subscribers.size === 0) {
        this.subscribers.delete(channel)
      }
    }
  }

  // Cleanup expired keys
  cleanup(): number {
    let cleaned = 0
    const now = Date.now()
    
    for (const [key, item] of this.data.entries()) {
      if (item.expiry && now > item.expiry) {
        this.data.delete(key)
        cleaned++
      }
    }
    
    return cleaned
  }
}

// Cache utilities
export class CacheManager {
  constructor(private redis: RedisClient, private defaultTTL = 3600) {}

  async get<T>(key: string, prefix?: string): Promise<T | null> {
    const fullKey = prefix ? `${prefix}:${key}` : key
    const value = await this.redis.get(fullKey)
    
    if (!value) return null
    
    try {
      return JSON.parse(value)
    } catch {
      return value as T
    }
  }

  async set<T>(key: string, value: T, options?: CacheOptions): Promise<void> {
    const fullKey = options?.prefix ? `${options.prefix}:${key}` : key
    const ttl = options?.ttl || this.defaultTTL
    
    await this.redis.set(fullKey, value, { EX: ttl })
  }

  async del(key: string, prefix?: string): Promise<boolean> {
    const fullKey = prefix ? `${prefix}:${key}` : key
    const result = await this.redis.del(fullKey)
    return result > 0
  }

  async exists(key: string, prefix?: string): Promise<boolean> {
    const fullKey = prefix ? `${prefix}:${key}` : key
    const result = await this.redis.exists(fullKey)
    return result > 0
  }

  // Cache with function execution
  async getOrSet<T>(
    key: string,
    fn: () => Promise<T>,
    options?: CacheOptions
  ): Promise<T> {
    const cached = await this.get<T>(key, options?.prefix)
    if (cached !== null) return cached
    
    const value = await fn()
    await this.set(key, value, options)
    return value
  }
}

// Queue utilities
export class QueueManager {
  constructor(private redis: RedisClient) {}

  async addJob(
    queueName: string,
    jobType: string,
    data: any,
    options?: {
      priority?: number
      delay?: number
      maxAttempts?: number
    }
  ): Promise<string> {
    const job: QueueJob = {
      id: `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: jobType,
      data,
      priority: options?.priority || 0,
      attempts: 0,
      maxAttempts: options?.maxAttempts || 3,
      createdAt: Date.now(),
      processAt: Date.now() + (options?.delay || 0),
    }

    await this.redis.lpush(queueName, job)
    return job.id
  }

  async getJob(queueName: string): Promise<QueueJob | null> {
    const job = await this.redis.rpop(queueName)
    if (!job) return null
    
    // Check if job is ready to process
    if (job.processAt > Date.now()) {
      // Put it back if not ready
      await this.redis.lpush(queueName, job)
      return null
    }
    
    return job
  }

  async getQueueLength(queueName: string): Promise<number> {
    return this.redis.llen(queueName)
  }

  async retryJob(queueName: string, job: QueueJob, delay = 5000): Promise<void> {
    job.attempts++
    job.processAt = Date.now() + delay
    
    if (job.attempts < job.maxAttempts) {
      await this.redis.lpush(queueName, job)
    } else {
      // Move to failed queue
      await this.redis.lpush(`${queueName}:failed`, job)
    }
  }
}

// Export singleton instances
export const redisClient = new RedisClient()
export const cacheManager = new CacheManager(redisClient)
export const queueManager = new QueueManager(redisClient)

// Start cleanup interval
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    const cleaned = redisClient.cleanup()
    if (cleaned > 0) {
      console.log(`Cleaned up ${cleaned} expired cache entries`)
    }
  }, 60 * 1000) // Run every minute
}
