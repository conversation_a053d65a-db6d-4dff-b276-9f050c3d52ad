{"name": "@clm/config", "version": "0.1.0", "private": true, "main": "index.js", "files": ["eslint-preset.js", "tailwind.config.js", "tsconfig.json"], "devDependencies": {"@types/node": "^20.14.10", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "prettier": "^3.2.5", "tailwindcss": "^3.4.4", "typescript": "^5.5.3"}}