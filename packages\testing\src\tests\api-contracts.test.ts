import { describe, it, expect, beforeEach, vi } from 'vitest'
import { TRPCError } from '@trpc/server'
import { contractsRouter } from '@clm/api/src/routers/contracts'
import { createTRPCMsw } from 'msw-trpc'
import { mockContracts, mockUsers } from '../utils/test-helpers'

// Mock Prisma
const mockPrisma = {
  contract: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    count: vi.fn(),
  },
}

vi.mock('@clm/db', () => ({
  prisma: mockPrisma,
  ContractStatus: {
    DRAFT: 'DRAFT',
    REVIEW: 'REVIEW',
    APPROVED: 'APPROVED',
    SIGNED: 'SIGNED',
    ACTIVE: 'ACTIVE',
    EXPIRED: 'EXPIRED',
    TERMINATED: 'TERMINATED',
    REJECTED: 'REJECTED',
  },
  Priority: {
    LOW: 'LOW',
    MEDIUM: 'MEDIUM',
    HIGH: 'HIGH',
    URGENT: 'URGENT',
  },
}))

// Mock context
const createMockContext = (user = mockUsers.admin) => ({
  user,
  session: { id: 'test-session', userId: user.id },
})

describe('Contracts Router', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getAll', () => {
    it('should return paginated contracts', async () => {
      const mockContractsData = [mockContracts.active, mockContracts.draft]
      mockPrisma.contract.findMany.mockResolvedValue(mockContractsData)

      const caller = contractsRouter.createCaller(createMockContext())
      
      const result = await caller.getAll({
        limit: 10,
        cursor: undefined,
      })

      expect(result.contracts).toEqual(mockContractsData)
      expect(mockPrisma.contract.findMany).toHaveBeenCalledWith({
        take: 11, // limit + 1 for cursor
        where: {},
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          parties: true,
          _count: {
            select: {
              comments: true,
              versions: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
      })
    })

    it('should filter contracts by status', async () => {
      const mockContractsData = [mockContracts.active]
      mockPrisma.contract.findMany.mockResolvedValue(mockContractsData)

      const caller = contractsRouter.createCaller(createMockContext())
      
      await caller.getAll({
        limit: 10,
        status: 'ACTIVE',
      })

      expect(mockPrisma.contract.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            status: 'ACTIVE',
          },
        })
      )
    })

    it('should search contracts by title and description', async () => {
      const mockContractsData = [mockContracts.active]
      mockPrisma.contract.findMany.mockResolvedValue(mockContractsData)

      const caller = contractsRouter.createCaller(createMockContext())
      
      await caller.getAll({
        limit: 10,
        search: 'test contract',
      })

      expect(mockPrisma.contract.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            OR: [
              {
                title: {
                  contains: 'test contract',
                  mode: 'insensitive',
                },
              },
              {
                description: {
                  contains: 'test contract',
                  mode: 'insensitive',
                },
              },
            ],
          },
        })
      )
    })

    it('should handle cursor-based pagination', async () => {
      const mockContractsData = [mockContracts.active]
      mockPrisma.contract.findMany.mockResolvedValue(mockContractsData)

      const caller = contractsRouter.createCaller(createMockContext())
      
      await caller.getAll({
        limit: 10,
        cursor: 'test-cursor-id',
      })

      expect(mockPrisma.contract.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          cursor: {
            id: 'test-cursor-id',
          },
          skip: 1,
        })
      )
    })
  })

  describe('getById', () => {
    it('should return contract by id', async () => {
      mockPrisma.contract.findUnique.mockResolvedValue(mockContracts.active)

      const caller = contractsRouter.createCaller(createMockContext())
      
      const result = await caller.getById({ id: 'test-contract-id' })

      expect(result).toEqual(mockContracts.active)
      expect(mockPrisma.contract.findUnique).toHaveBeenCalledWith({
        where: { id: 'test-contract-id' },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
          parties: true,
          comments: {
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
          versions: {
            orderBy: {
              version: 'desc',
            },
          },
          aiAnalysis: true,
          workflow: {
            include: {
              steps: {
                include: {
                  assignee: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                    },
                  },
                },
                orderBy: {
                  order: 'asc',
                },
              },
            },
          },
        },
      })
    })

    it('should throw error if contract not found', async () => {
      mockPrisma.contract.findUnique.mockResolvedValue(null)

      const caller = contractsRouter.createCaller(createMockContext())
      
      await expect(caller.getById({ id: 'non-existent-id' })).rejects.toThrow(
        TRPCError
      )
    })
  })

  describe('create', () => {
    it('should create new contract', async () => {
      const newContract = {
        title: 'New Contract',
        description: 'Test contract description',
        content: 'Contract content',
        priority: 'MEDIUM' as const,
      }

      mockPrisma.contract.create.mockResolvedValue({
        ...mockContracts.draft,
        ...newContract,
      })

      const caller = contractsRouter.createCaller(createMockContext())
      
      const result = await caller.create(newContract)

      expect(result.title).toBe(newContract.title)
      expect(mockPrisma.contract.create).toHaveBeenCalledWith({
        data: {
          ...newContract,
          createdBy: mockUsers.admin.id,
          status: 'DRAFT',
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          parties: true,
        },
      })
    })

    it('should validate required fields', async () => {
      const caller = contractsRouter.createCaller(createMockContext())
      
      await expect(caller.create({
        title: '', // Empty title should fail validation
        description: 'Test',
      })).rejects.toThrow()
    })
  })

  describe('update', () => {
    it('should update existing contract', async () => {
      const updateData = {
        title: 'Updated Contract Title',
        description: 'Updated description',
      }

      mockPrisma.contract.findUnique.mockResolvedValue(mockContracts.draft)
      mockPrisma.contract.update.mockResolvedValue({
        ...mockContracts.draft,
        ...updateData,
      })

      const caller = contractsRouter.createCaller(createMockContext())
      
      const result = await caller.update({
        id: 'test-contract-id',
        ...updateData,
      })

      expect(result.title).toBe(updateData.title)
      expect(mockPrisma.contract.update).toHaveBeenCalledWith({
        where: { id: 'test-contract-id' },
        data: updateData,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          parties: true,
        },
      })
    })

    it('should throw error if contract not found', async () => {
      mockPrisma.contract.findUnique.mockResolvedValue(null)

      const caller = contractsRouter.createCaller(createMockContext())
      
      await expect(caller.update({
        id: 'non-existent-id',
        title: 'Updated Title',
      })).rejects.toThrow(TRPCError)
    })
  })

  describe('delete', () => {
    it('should delete contract', async () => {
      mockPrisma.contract.findUnique.mockResolvedValue(mockContracts.draft)
      mockPrisma.contract.delete.mockResolvedValue(mockContracts.draft)

      const caller = contractsRouter.createCaller(createMockContext())
      
      const result = await caller.delete({ id: 'test-contract-id' })

      expect(result.success).toBe(true)
      expect(mockPrisma.contract.delete).toHaveBeenCalledWith({
        where: { id: 'test-contract-id' },
      })
    })

    it('should throw error if contract not found', async () => {
      mockPrisma.contract.findUnique.mockResolvedValue(null)

      const caller = contractsRouter.createCaller(createMockContext())
      
      await expect(caller.delete({ id: 'non-existent-id' })).rejects.toThrow(
        TRPCError
      )
    })
  })
})
