{"name": "@clm/db", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/seed.ts", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@prisma/client": "^5.15.0", "bcryptjs": "^2.4.3", "nanoid": "^5.0.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20.14.10", "eslint": "^8.57.0", "prisma": "^5.15.0", "tsx": "^4.16.2", "typescript": "^5.5.3"}}