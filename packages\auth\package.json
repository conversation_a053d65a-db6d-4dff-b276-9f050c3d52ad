{"name": "@clm/auth", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@clm/db": "*", "better-auth": "^0.7.1", "bcryptjs": "^2.4.3", "zod": "^3.23.8", "jotai": "^2.8.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20.14.10", "eslint": "^8.57.0", "typescript": "^5.5.3"}}