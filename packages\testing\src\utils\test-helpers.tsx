import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider } from '@clm/auth'

// Mock user data for testing
export const mockUsers = {
  admin: {
    id: 'test-admin-id',
    email: '<EMAIL>',
    name: 'Admin CLM',
    role: 'ADMIN' as const,
    isActive: true,
    avatar: null,
    emailVerified: true,
  },
  legal: {
    id: 'test-legal-id',
    email: '<EMAIL>',
    name: 'Legal Team',
    role: 'LEGAL' as const,
    isActive: true,
    avatar: null,
    emailVerified: true,
  },
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Regular User',
    role: 'USER' as const,
    isActive: true,
    avatar: null,
    emailVerified: true,
  },
  viewer: {
    id: 'test-viewer-id',
    email: '<EMAIL>',
    name: 'Viewer User',
    role: 'VIEWER' as const,
    isActive: true,
    avatar: null,
    emailVerified: true,
  },
}

// Mock session data
export const createMockSession = (user: typeof mockUsers.admin) => ({
  user,
  session: {
    id: 'test-session-id',
    userId: user.id,
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    token: 'test-session-token',
  },
})

// Test wrapper component
interface TestWrapperProps {
  children: React.ReactNode
  user?: typeof mockUsers.admin
  requireAuth?: boolean
}

function TestWrapper({ children, user = mockUsers.admin, requireAuth = false }: TestWrapperProps) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider requireAuth={requireAuth}>
        {children}
      </AuthProvider>
    </QueryClientProvider>
  )
}

// Custom render function
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  user?: typeof mockUsers.admin
  requireAuth?: boolean
}

export function renderWithProviders(
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) {
  const { user, requireAuth, ...renderOptions } = options

  return render(ui, {
    wrapper: ({ children }) => (
      <TestWrapper user={user} requireAuth={requireAuth}>
        {children}
      </TestWrapper>
    ),
    ...renderOptions,
  })
}

// Mock contract data
export const mockContracts = {
  active: {
    id: 'test-contract-1',
    title: 'Service Agreement Q1 2024',
    description: 'Quarterly service agreement for software development',
    status: 'ACTIVE' as const,
    priority: 'HIGH' as const,
    value: 50000000,
    currency: 'IDR',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-03-31'),
    createdBy: 'test-admin-id',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15'),
    creator: mockUsers.admin,
    parties: [
      {
        id: 'party-1',
        name: 'PT. Client Indonesia',
        email: '<EMAIL>',
        phone: '+62-21-1234567',
        address: 'Jakarta, Indonesia',
        role: 'CLIENT' as const,
      },
    ],
    comments: [],
    workflowSteps: [],
    _count: {
      comments: 0,
      versions: 1,
    },
  },
  draft: {
    id: 'test-contract-2',
    title: 'NDA Agreement - Tech Partnership',
    description: 'Non-disclosure agreement for technology partnership',
    status: 'DRAFT' as const,
    priority: 'MEDIUM' as const,
    value: null,
    currency: 'IDR',
    startDate: null,
    endDate: null,
    createdBy: 'test-user-id',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
    creator: mockUsers.user,
    parties: [],
    comments: [],
    workflowSteps: [],
    _count: {
      comments: 0,
      versions: 1,
    },
  },
}

// Mock notification data
export const mockNotifications = [
  {
    id: 'notification-1',
    title: 'Kontrak Baru Dibuat',
    message: 'Kontrak "Service Agreement Q1 2024" telah dibuat',
    type: 'CONTRACT_CREATED' as const,
    userId: 'test-legal-id',
    contractId: 'test-contract-1',
    isRead: false,
    createdAt: new Date('2024-01-01T10:00:00.000Z'),
    contract: {
      id: 'test-contract-1',
      title: 'Service Agreement Q1 2024',
    },
  },
  {
    id: 'notification-2',
    title: 'Tugas Workflow Baru',
    message: 'Anda ditugaskan untuk "Legal Review"',
    type: 'WORKFLOW_ASSIGNED' as const,
    userId: 'test-legal-id',
    contractId: 'test-contract-1',
    isRead: true,
    createdAt: new Date('2024-01-01T09:00:00.000Z'),
    contract: {
      id: 'test-contract-1',
      title: 'Service Agreement Q1 2024',
    },
  },
]

// Utility functions for testing
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const createMockFile = (name: string, type: string, size: number = 1024) => {
  const file = new File(['test content'], name, { type })
  Object.defineProperty(file, 'size', { value: size })
  return file
}

export const mockLocalStorage = () => {
  const storage: Record<string, string> = {}
  
  return {
    getItem: (key: string) => storage[key] || null,
    setItem: (key: string, value: string) => {
      storage[key] = value
    },
    removeItem: (key: string) => {
      delete storage[key]
    },
    clear: () => {
      Object.keys(storage).forEach(key => delete storage[key])
    },
    length: Object.keys(storage).length,
    key: (index: number) => Object.keys(storage)[index] || null,
  }
}

export const mockSessionStorage = () => {
  const storage: Record<string, string> = {}
  
  return {
    getItem: (key: string) => storage[key] || null,
    setItem: (key: string, value: string) => {
      storage[key] = value
    },
    removeItem: (key: string) => {
      delete storage[key]
    },
    clear: () => {
      Object.keys(storage).forEach(key => delete storage[key])
    },
    length: Object.keys(storage).length,
    key: (index: number) => Object.keys(storage)[index] || null,
  }
}

// Database testing utilities
export const createTestDatabase = async () => {
  // This would set up a test database instance
  // For now, we'll use mocks
  return {
    cleanup: async () => {
      // Clean up test data
    },
    seed: async () => {
      // Seed test data
    },
  }
}

// WebSocket testing utilities
export const createMockWebSocket = () => {
  const listeners: Record<string, Function[]> = {}
  
  return {
    addEventListener: (event: string, callback: Function) => {
      if (!listeners[event]) listeners[event] = []
      listeners[event].push(callback)
    },
    removeEventListener: (event: string, callback: Function) => {
      if (listeners[event]) {
        listeners[event] = listeners[event].filter(cb => cb !== callback)
      }
    },
    send: (data: string) => {
      // Mock send
    },
    close: () => {
      // Mock close
    },
    emit: (event: string, data: any) => {
      if (listeners[event]) {
        listeners[event].forEach(callback => callback(data))
      }
    },
    readyState: 1,
  }
}

// Form testing utilities
export const fillForm = async (form: HTMLFormElement, data: Record<string, string>) => {
  const { userEvent } = await import('@testing-library/user-event')
  const user = userEvent.setup()
  
  for (const [name, value] of Object.entries(data)) {
    const field = form.querySelector(`[name="${name}"]`) as HTMLInputElement
    if (field) {
      await user.clear(field)
      await user.type(field, value)
    }
  }
}

// API testing utilities
export const mockApiResponse = <T>(data: T, delay: number = 0) => {
  return new Promise<T>((resolve) => {
    setTimeout(() => resolve(data), delay)
  })
}

export const mockApiError = (message: string, status: number = 400, delay: number = 0) => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(message))
    }, delay)
  })
}

// Re-export testing library utilities
export * from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'
