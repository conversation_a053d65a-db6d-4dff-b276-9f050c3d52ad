import { useState } from 'react'
import { Button, Input, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@clm/ui'
import Link from 'next/link'

interface Banner {
  id: string
  title: string
  message: string
  type: 'info' | 'warning' | 'error' | 'success'
  isActive: boolean
  startDate?: string
  endDate?: string
  targetPages: string[]
  createdAt: string
}

export default function BannersManagementPage() {
  const [banners, setBanners] = useState<Banner[]>([
    {
      id: '1',
      title: 'System Maintenance',
      message: 'Scheduled maintenance will occur on Sunday, 2:00 AM - 4:00 AM WIB.',
      type: 'warning',
      isActive: true,
      startDate: '2024-07-28',
      endDate: '2024-07-30',
      targetPages: ['dashboard', 'contracts'],
      createdAt: '2024-07-25T10:00:00Z',
    },
    {
      id: '2',
      title: 'New Feature Available',
      message: 'Real-time collaboration is now available for all contract editing!',
      type: 'success',
      isActive: false,
      targetPages: ['all'],
      createdAt: '2024-07-20T15:30:00Z',
    },
  ])

  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newBanner, setNewBanner] = useState({
    title: '',
    message: '',
    type: 'info' as Banner['type'],
    startDate: '',
    endDate: '',
    targetPages: ['all'],
  })

  const handleCreateBanner = () => {
    const banner: Banner = {
      id: Date.now().toString(),
      ...newBanner,
      isActive: true,
      createdAt: new Date().toISOString(),
    }
    setBanners([banner, ...banners])
    setNewBanner({
      title: '',
      message: '',
      type: 'info',
      startDate: '',
      endDate: '',
      targetPages: ['all'],
    })
    setShowCreateForm(false)
  }

  const toggleBannerStatus = (id: string) => {
    setBanners(banners.map(banner => 
      banner.id === id ? { ...banner, isActive: !banner.isActive } : banner
    ))
  }

  const deleteBanner = (id: string) => {
    if (confirm('Are you sure you want to delete this banner?')) {
      setBanners(banners.filter(banner => banner.id !== id))
    }
  }

  const getBannerTypeColor = (type: Banner['type']) => {
    switch (type) {
      case 'info': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'error': return 'bg-red-100 text-red-800 border-red-200'
      case 'success': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getBannerIcon = (type: Banner['type']) => {
    switch (type) {
      case 'info': return 'ℹ️'
      case 'warning': return '⚠️'
      case 'error': return '❌'
      case 'success': return '✅'
      default: return 'ℹ️'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link href="/admin" className="text-blue-600 hover:text-blue-800 text-sm">
                ← Back to Admin Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mt-2">Banner Management</h1>
              <p className="text-gray-600">Manage system-wide banners and announcements</p>
            </div>
            <Button onClick={() => setShowCreateForm(true)}>
              📢 Create New Banner
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Create Banner Form */}
        {showCreateForm && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Create New Banner</CardTitle>
              <CardDescription>
                Create a new system banner or announcement
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Banner Title *
                  </label>
                  <Input
                    value={newBanner.title}
                    onChange={(e) => setNewBanner({ ...newBanner, title: e.target.value })}
                    placeholder="Enter banner title"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Banner Type *
                  </label>
                  <select
                    value={newBanner.type}
                    onChange={(e) => setNewBanner({ ...newBanner, type: e.target.value as Banner['type'] })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="info">Info - General information</option>
                    <option value="success">Success - Positive announcements</option>
                    <option value="warning">Warning - Important notices</option>
                    <option value="error">Error - Critical alerts</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Banner Message *
                </label>
                <textarea
                  value={newBanner.message}
                  onChange={(e) => setNewBanner({ ...newBanner, message: e.target.value })}
                  placeholder="Enter banner message"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date (Optional)
                  </label>
                  <Input
                    type="date"
                    value={newBanner.startDate}
                    onChange={(e) => setNewBanner({ ...newBanner, startDate: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    End Date (Optional)
                  </label>
                  <Input
                    type="date"
                    value={newBanner.endDate}
                    onChange={(e) => setNewBanner({ ...newBanner, endDate: e.target.value })}
                    min={newBanner.startDate}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleCreateBanner}
                  disabled={!newBanner.title || !newBanner.message}
                >
                  Create Banner
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Banners List */}
        <div className="space-y-4">
          {banners.length > 0 ? (
            banners.map((banner) => (
              <Card key={banner.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <span className="text-xl">{getBannerIcon(banner.type)}</span>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {banner.title}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${getBannerTypeColor(banner.type)}`}>
                          {banner.type.toUpperCase()}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          banner.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {banner.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      
                      <p className="text-gray-700 mb-3">
                        {banner.message}
                      </p>
                      
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                        <span>
                          📅 Created {new Date(banner.createdAt).toLocaleDateString('id-ID')}
                        </span>
                        {banner.startDate && (
                          <span>
                            🚀 Starts {new Date(banner.startDate).toLocaleDateString('id-ID')}
                          </span>
                        )}
                        {banner.endDate && (
                          <span>
                            🏁 Ends {new Date(banner.endDate).toLocaleDateString('id-ID')}
                          </span>
                        )}
                        <span>
                          🎯 Target: {banner.targetPages.join(', ')}
                        </span>
                      </div>

                      {/* Banner Preview */}
                      <div className={`mt-4 p-3 rounded-lg border ${getBannerTypeColor(banner.type)}`}>
                        <div className="flex items-center space-x-2">
                          <span>{getBannerIcon(banner.type)}</span>
                          <strong>{banner.title}</strong>
                        </div>
                        <p className="mt-1 text-sm">{banner.message}</p>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-2 ml-4">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => toggleBannerStatus(banner.id)}
                        className={banner.isActive ? 'text-orange-600' : 'text-green-600'}
                      >
                        {banner.isActive ? '⏸️ Deactivate' : '▶️ Activate'}
                      </Button>
                      <Button variant="outline" size="sm">
                        ✏️ Edit
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-red-600 hover:text-red-700"
                        onClick={() => deleteBanner(banner.id)}
                      >
                        🗑️ Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">📢</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No banners created yet
                  </h3>
                  <p className="text-gray-500 mb-6">
                    Create your first system banner to communicate with users.
                  </p>
                  <Button onClick={() => setShowCreateForm(true)}>
                    📢 Create First Banner
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
