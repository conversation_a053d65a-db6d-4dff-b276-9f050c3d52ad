import { z } from 'zod'
import bcrypt from 'bcryptjs'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure, adminProcedure } from '../trpc'
import { prisma, UserRole } from '@clm/db'

export const usersRouter = createTRPCRouter({
  getAll: adminProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(10),
        cursor: z.string().optional(),
        role: z.nativeEnum(UserRole).optional(),
        search: z.string().optional(),
        isActive: z.boolean().optional(),
      })
    )
    .query(async ({ input }) => {
      const { limit, cursor, role, search, isActive } = input

      const where = {
        ...(role && { role }),
        ...(isActive !== undefined && { isActive }),
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } },
          ],
        }),
      }

      const users = await prisma.user.findMany({
        where,
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          avatar: true,
          isActive: true,
          createdAt: true,
          _count: {
            select: {
              contracts: true,
              comments: true,
            },
          },
        },
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (users.length > limit) {
        const nextItem = users.pop()
        nextCursor = nextItem!.id
      }

      return {
        users,
        nextCursor,
      }
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      const user = await prisma.user.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          avatar: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              contracts: true,
              comments: true,
              workflowSteps: true,
            },
          },
        },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      // Users can only view their own profile unless they're admin
      if (user.id !== ctx.session.user.id && ctx.session.user.role !== 'ADMIN') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      return user
    }),

  create: adminProcedure
    .input(
      z.object({
        email: z.string().email(),
        name: z.string().min(1),
        password: z.string().min(6),
        role: z.nativeEnum(UserRole).default('USER'),
        avatar: z.string().url().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const { email, name, password, role, avatar } = input

      const existingUser = await prisma.user.findUnique({
        where: { email },
      })

      if (existingUser) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'User with this email already exists',
        })
      }

      const hashedPassword = await bcrypt.hash(password, 12)

      const user = await prisma.user.create({
        data: {
          email,
          name,
          role,
          avatar,
          accounts: {
            create: {
              accountId: email,
              providerId: 'credential',
              password: hashedPassword,
            }
          }
        },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          avatar: true,
          isActive: true,
          createdAt: true,
        },
      })

      // Create audit log
      await prisma.auditLog.create({
        data: {
          action: 'CREATE',
          entity: 'User',
          entityId: user.id,
          newValues: { email, name, role },
          userId: ctx.session.user.id,
        },
      })

      return user
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1).optional(),
        role: z.nativeEnum(UserRole).optional(),
        avatar: z.string().url().optional(),
        isActive: z.boolean().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const { id, ...updateData } = input

      // Users can only update their own profile unless they're admin
      if (id !== ctx.session.user.id && ctx.session.user.role !== 'ADMIN') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      // Only admin can change role and isActive
      if ((updateData.role || updateData.isActive !== undefined) && ctx.session.user.role !== 'ADMIN') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only admin can change role or active status',
        })
      }

      const currentUser = await prisma.user.findUnique({
        where: { id },
      })

      if (!currentUser) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      const user = await prisma.user.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          avatar: true,
          isActive: true,
          updatedAt: true,
        },
      })

      // Create audit log
      await prisma.auditLog.create({
        data: {
          action: 'UPDATE',
          entity: 'User',
          entityId: user.id,
          oldValues: currentUser,
          newValues: updateData,
          userId: ctx.session.user.id,
        },
      })

      return user
    }),

  changePassword: protectedProcedure
    .input(
      z.object({
        currentPassword: z.string(),
        newPassword: z.string().min(6),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const { currentPassword, newPassword } = input

      const user = await prisma.user.findUnique({
        where: { id: ctx.session.user.id },
        include: {
          accounts: {
            where: { providerId: 'credential' }
          }
        }
      })

      if (!user || !user.accounts[0]?.password) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User or password not found',
        })
      }

      const isValidPassword = await bcrypt.compare(currentPassword, user.accounts[0].password)

      if (!isValidPassword) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Current password is incorrect',
        })
      }

      const hashedNewPassword = await bcrypt.hash(newPassword, 12)

      await prisma.account.update({
        where: { 
          providerId_accountId: {
            providerId: 'credential',
            accountId: user.email
          }
        },
        data: { password: hashedNewPassword },
      })

      // Create audit log
      await prisma.auditLog.create({
        data: {
          action: 'UPDATE',
          entity: 'User',
          entityId: user.id,
          newValues: { passwordChanged: true },
          userId: ctx.session.user.id,
        },
      })

      return { success: true }
    }),

  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const user = await prisma.user.findUnique({
        where: { id: input.id },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      // Prevent admin from deleting themselves
      if (user.id === ctx.session.user.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot delete your own account',
        })
      }

      await prisma.user.delete({
        where: { id: input.id },
      })

      // Create audit log
      await prisma.auditLog.create({
        data: {
          action: 'DELETE',
          entity: 'User',
          entityId: user.id,
          oldValues: user,
          userId: ctx.session.user.id,
        },
      })

      return { success: true }
    }),

  deactivate: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const user = await prisma.user.findUnique({
        where: { id: input.id },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      // Prevent admin from deactivating themselves
      if (user.id === ctx.session.user.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot deactivate your own account',
        })
      }

      const updatedUser = await prisma.user.update({
        where: { id: input.id },
        data: { isActive: false },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
        },
      })

      // Create audit log
      await prisma.auditLog.create({
        data: {
          action: 'UPDATE',
          entity: 'User',
          entityId: user.id,
          oldValues: { isActive: user.isActive },
          newValues: { isActive: false },
          userId: ctx.session.user.id,
        },
      })

      return updatedUser
    }),
})
