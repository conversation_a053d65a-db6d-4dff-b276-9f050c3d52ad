import { AIService } from './ai-service';

export async function extractDetailedMetadata(contractText: string) {
  const prompt = `Extract the following detailed metadata from the contract and return it as a JSON object:

- Parties: [Name, Address, Role]
- Dates: [Effective Date, Expiration Date]
- Values: [Contract Value, Currency]
- Terms: [Payment Terms, Renewal Terms]

${contractText}`;
  const result = await AIService.generateContent(prompt);
  try {
    return JSON.parse(result);
  } catch (error) {
    console.error('Error parsing metadata:', error);
    return null;
  }
}
