import { PrismaClient } from '@prisma/client'

declare global {
  // eslint-disable-next-line no-var
  var __prisma: PrismaClient | undefined
}

export const prisma =
  globalThis.__prisma ??
  new PrismaClient({
    log: ['query'],
  })

if (process.env.NODE_ENV !== 'production') {
  globalThis.__prisma = prisma
}

export * from '@prisma/client'
export type { User, Contract, Workflow, Comment } from '@prisma/client'
export { ContractStatus, Priority } from '@prisma/client'
export * from './migrations'
export * from './repositories/base'
export * from './repositories/auth'
export * from './repositories/contracts'
export * from './repositories/users'
