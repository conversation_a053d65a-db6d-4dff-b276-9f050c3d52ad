{"$schema": "https://json.schemastore.org/tsconfig", "display": "Base TypeScript Config", "compilerOptions": {"composite": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "target": "es2022", "module": "ESNext", "lib": ["es2022"], "allowJs": true, "checkJs": false, "incremental": true, "resolveJsonModule": true}, "exclude": ["node_modules"]}