import { http, HttpResponse } from 'msw'

const mockUsers = [
  {
    id: 'test-admin-id',
    email: '<EMAIL>',
    name: 'Admin CLM',
    role: 'ADMIN',
    isActive: true,
    avatar: null,
    emailVerified: true,
  },
  {
    id: 'test-legal-id',
    email: '<EMAIL>',
    name: 'Legal Team',
    role: 'LEGAL',
    isActive: true,
    avatar: null,
    emailVerified: true,
  },
  {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Regular User',
    role: 'USER',
    isActive: true,
    avatar: null,
    emailVerified: true,
  },
]

export const authHandlers = [
  // Sign in
  http.post('/api/auth/sign-in', async ({ request }) => {
    const body = await request.json() as any
    const { email, password } = body

    const user = mockUsers.find(u => u.email === email)
    
    if (!user) {
      return HttpResponse.json(
        { error: { message: 'User not found' } },
        { status: 401 }
      )
    }

    // Mock password validation (in real app, this would be hashed)
    const validPasswords: Record<string, string> = {
      '<EMAIL>': 'admin123',
      '<EMAIL>': 'legal123',
      '<EMAIL>': 'user123',
    }

    if (validPasswords[email] !== password) {
      return HttpResponse.json(
        { error: { message: 'Invalid password' } },
        { status: 401 }
      )
    }

    return HttpResponse.json({
      data: {
        user,
        session: {
          id: 'test-session-id',
          userId: user.id,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          token: 'test-session-token',
        },
      },
    })
  }),

  // Sign up
  http.post('/api/auth/sign-up', async ({ request }) => {
    const body = await request.json() as any
    const { email, password, name } = body

    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === email)
    if (existingUser) {
      return HttpResponse.json(
        { error: { message: 'User already exists' } },
        { status: 400 }
      )
    }

    const newUser = {
      id: 'test-new-user-id',
      email,
      name,
      role: 'USER' as const,
      isActive: true,
      avatar: null,
      emailVerified: false,
    }

    mockUsers.push(newUser)

    return HttpResponse.json({
      data: {
        user: newUser,
        session: {
          id: 'test-session-id',
          userId: newUser.id,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          token: 'test-session-token',
        },
      },
    })
  }),

  // Get session
  http.get('/api/auth/session', ({ request }) => {
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { error: { message: 'No authorization header' } },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    
    if (token !== 'test-session-token') {
      return HttpResponse.json(
        { error: { message: 'Invalid token' } },
        { status: 401 }
      )
    }

    return HttpResponse.json({
      data: {
        user: mockUsers[0], // Return admin user for tests
        session: {
          id: 'test-session-id',
          userId: mockUsers[0].id,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          token: 'test-session-token',
        },
      },
    })
  }),

  // Sign out
  http.post('/api/auth/sign-out', () => {
    return HttpResponse.json({ data: { success: true } })
  }),

  // Update user
  http.patch('/api/auth/user', async ({ request }) => {
    const body = await request.json() as any
    const { name, avatar } = body

    const updatedUser = {
      ...mockUsers[0],
      name: name || mockUsers[0].name,
      avatar: avatar || mockUsers[0].avatar,
    }

    return HttpResponse.json({
      data: updatedUser,
    })
  }),

  // Change password
  http.post('/api/auth/change-password', async ({ request }) => {
    const body = await request.json() as any
    const { currentPassword, newPassword } = body

    // Mock password validation
    if (currentPassword !== 'admin123') {
      return HttpResponse.json(
        { error: { message: 'Current password is incorrect' } },
        { status: 400 }
      )
    }

    return HttpResponse.json({
      data: { success: true },
    })
  }),

  // List users (admin only)
  http.get('/api/auth/users', ({ request }) => {
    const url = new URL(request.url)
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const offset = parseInt(url.searchParams.get('offset') || '0')
    const search = url.searchParams.get('search')

    let filteredUsers = mockUsers
    
    if (search) {
      filteredUsers = mockUsers.filter(user => 
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
      )
    }

    const paginatedUsers = filteredUsers.slice(offset, offset + limit)

    return HttpResponse.json({
      data: paginatedUsers,
    })
  }),
]
