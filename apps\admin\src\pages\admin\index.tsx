import { useAuth } from '@clm/auth'
import { trpc } from '@/lib/trpc'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button } from '@clm/ui'
import Link from 'next/link'

export default function AdminDashboard() {
  const { user } = useAuth()
  const { data: userStats, isLoading: userStatsLoading } = trpc.auth.getUserStats.useQuery()
  const { data: contractStats, isLoading: contractStatsLoading } = trpc.contracts.getStatistics.useQuery()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">CLM Admin Panel</h1>
              <p className="text-gray-600">Developer & Administrator Dashboard</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Welcome, {user?.name}
              </span>
              <Button variant="outline" size="sm">
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Total Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {userStatsLoading ? '...' : userStats?.total || 0}
              </div>
              <p className="text-xs text-gray-500">
                {userStatsLoading ? '...' : `${userStats?.active || 0} active`}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Total Contracts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {contractStatsLoading ? '...' : contractStats?.total || 0}
              </div>
              <p className="text-xs text-gray-500">
                {contractStatsLoading ? '...' : `${contractStats?.byStatus?.active || 0} active`}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Admin Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {userStatsLoading ? '...' : userStats?.byRole?.ADMIN || 0}
              </div>
              <p className="text-xs text-gray-500">
                System administrators
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">System Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                ✓ Online
              </div>
              <p className="text-xs text-gray-500">
                All systems operational
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Admin Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>👥 User Management</CardTitle>
              <CardDescription>
                Manage users, roles, and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/admin/users">
                  <Button className="w-full justify-start">
                    View All Users
                  </Button>
                </Link>
                <Link href="/admin/users/create">
                  <Button variant="outline" className="w-full justify-start">
                    Create New User
                  </Button>
                </Link>
                <Link href="/admin/users/admins">
                  <Button variant="outline" className="w-full justify-start">
                    Manage Admins
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>📄 Contract Management</CardTitle>
              <CardDescription>
                Monitor and manage all contracts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/admin/contracts">
                  <Button className="w-full justify-start">
                    View All Contracts
                  </Button>
                </Link>
                <Link href="/admin/contracts/templates">
                  <Button variant="outline" className="w-full justify-start">
                    Manage Templates
                  </Button>
                </Link>
                <Link href="/admin/contracts/workflows">
                  <Button variant="outline" className="w-full justify-start">
                    Workflow Settings
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>⚙️ System Settings</CardTitle>
              <CardDescription>
                Configure system-wide settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/admin/settings">
                  <Button className="w-full justify-start">
                    General Settings
                  </Button>
                </Link>
                <Link href="/admin/settings/banners">
                  <Button variant="outline" className="w-full justify-start">
                    Manage Banners
                  </Button>
                </Link>
                <Link href="/admin/settings/maintenance">
                  <Button variant="outline" className="w-full justify-start">
                    Maintenance Mode
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>📊 Analytics & Reports</CardTitle>
              <CardDescription>
                View system analytics and reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/admin/analytics">
                  <Button className="w-full justify-start">
                    System Analytics
                  </Button>
                </Link>
                <Link href="/admin/audit-logs">
                  <Button variant="outline" className="w-full justify-start">
                    Audit Logs
                  </Button>
                </Link>
                <Link href="/admin/reports">
                  <Button variant="outline" className="w-full justify-start">
                    Generate Reports
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>🔧 Developer Tools</CardTitle>
              <CardDescription>
                Tools for developers and debugging
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/admin/dev/database">
                  <Button className="w-full justify-start">
                    Database Console
                  </Button>
                </Link>
                <Link href="/admin/dev/logs">
                  <Button variant="outline" className="w-full justify-start">
                    System Logs
                  </Button>
                </Link>
                <Link href="/admin/dev/api">
                  <Button variant="outline" className="w-full justify-start">
                    API Testing
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>🚨 Issues & Support</CardTitle>
              <CardDescription>
                Handle user issues and support tickets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/admin/issues">
                  <Button className="w-full justify-start">
                    View Issues
                  </Button>
                </Link>
                <Link href="/admin/support">
                  <Button variant="outline" className="w-full justify-start">
                    Support Tickets
                  </Button>
                </Link>
                <Link href="/admin/feedback">
                  <Button variant="outline" className="w-full justify-start">
                    User Feedback
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest system activities and user actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userStats?.recentUsers?.map((user: any) => (
                  <div key={user.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-0">
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-sm text-gray-500">{user.email} • {user.role}</p>
                    </div>
                    <div className="text-sm text-gray-500">
                      Joined {new Date(user.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                )) || (
                  <p className="text-gray-500 text-center py-4">No recent activity</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
