import { http, HttpResponse } from 'msw'

const mockContracts = [
  {
    id: 'test-contract-1',
    title: 'Service Agreement Q1 2024',
    description: 'Quarterly service agreement for software development',
    status: 'ACTIVE',
    priority: 'HIGH',
    value: 50000000,
    currency: 'IDR',
    startDate: '2024-01-01T00:00:00.000Z',
    endDate: '2024-03-31T23:59:59.999Z',
    createdBy: 'test-admin-id',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-15T10:30:00.000Z',
    creator: {
      id: 'test-admin-id',
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
    },
    parties: [
      {
        id: 'party-1',
        name: 'PT. Client Indonesia',
        email: '<EMAIL>',
        phone: '+62-21-1234567',
        address: 'Jakarta, Indonesia',
        role: 'CLIENT',
      },
      {
        id: 'party-2',
        name: 'PT. Vendor Solutions',
        email: '<EMAIL>',
        phone: '+62-21-7654321',
        address: 'Bandung, Indonesia',
        role: 'VENDOR',
      },
    ],
    comments: [
      {
        id: 'comment-1',
        content: 'Please review the payment terms in section 3.',
        authorId: 'test-legal-id',
        author: {
          id: 'test-legal-id',
          name: 'Legal Team',
          avatar: null,
        },
        createdAt: '2024-01-10T14:30:00.000Z',
      },
    ],
    workflowSteps: [
      {
        id: 'step-1',
        name: 'Legal Review',
        description: 'Review contract terms and conditions',
        status: 'COMPLETED',
        assignedTo: 'test-legal-id',
        assignee: {
          id: 'test-legal-id',
          name: 'Legal Team',
        },
        completedAt: '2024-01-05T16:00:00.000Z',
      },
      {
        id: 'step-2',
        name: 'Finance Approval',
        description: 'Approve budget and payment terms',
        status: 'IN_PROGRESS',
        assignedTo: 'test-admin-id',
        assignee: {
          id: 'test-admin-id',
          name: 'Admin CLM',
        },
        completedAt: null,
      },
    ],
    _count: {
      comments: 1,
      versions: 3,
    },
  },
  {
    id: 'test-contract-2',
    title: 'NDA Agreement - Tech Partnership',
    description: 'Non-disclosure agreement for technology partnership',
    status: 'DRAFT',
    priority: 'MEDIUM',
    value: null,
    currency: 'IDR',
    startDate: null,
    endDate: null,
    createdBy: 'test-user-id',
    createdAt: '2024-01-20T09:15:00.000Z',
    updatedAt: '2024-01-20T09:15:00.000Z',
    creator: {
      id: 'test-user-id',
      name: 'Regular User',
      email: '<EMAIL>',
    },
    parties: [],
    comments: [],
    workflowSteps: [],
    _count: {
      comments: 0,
      versions: 1,
    },
  },
]

export const contractHandlers = [
  // Get all contracts
  http.get('/api/trpc/contracts.getAll', ({ request }) => {
    const url = new URL(request.url)
    const input = JSON.parse(url.searchParams.get('input') || '{}')
    const { limit = 10, search, status } = input

    let filteredContracts = mockContracts

    if (search) {
      filteredContracts = mockContracts.filter(contract =>
        contract.title.toLowerCase().includes(search.toLowerCase()) ||
        contract.description?.toLowerCase().includes(search.toLowerCase())
      )
    }

    if (status) {
      filteredContracts = filteredContracts.filter(contract => contract.status === status)
    }

    const paginatedContracts = filteredContracts.slice(0, limit)

    return HttpResponse.json({
      result: {
        data: {
          contracts: paginatedContracts,
          nextCursor: paginatedContracts.length === limit ? 'next-cursor' : null,
        },
      },
    })
  }),

  // Get contract by ID
  http.get('/api/trpc/contracts.getById', ({ request }) => {
    const url = new URL(request.url)
    const input = JSON.parse(url.searchParams.get('input') || '{}')
    const { id } = input

    const contract = mockContracts.find(c => c.id === id)

    if (!contract) {
      return HttpResponse.json(
        {
          error: {
            message: 'Contract not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    return HttpResponse.json({
      result: {
        data: contract,
      },
    })
  }),

  // Create contract
  http.post('/api/trpc/contracts.create', async ({ request }) => {
    const body = await request.json() as any
    const contractData = body

    const newContract = {
      id: 'test-new-contract-id',
      title: contractData.title,
      description: contractData.description || null,
      status: 'DRAFT',
      priority: contractData.priority || 'MEDIUM',
      value: contractData.value || null,
      currency: contractData.currency || 'IDR',
      startDate: contractData.startDate || null,
      endDate: contractData.endDate || null,
      createdBy: 'test-admin-id',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      creator: {
        id: 'test-admin-id',
        name: 'Admin CLM',
        email: '<EMAIL>',
      },
      parties: contractData.parties || [],
      comments: [],
      workflowSteps: [],
      _count: {
        comments: 0,
        versions: 1,
      },
    }

    mockContracts.push(newContract)

    return HttpResponse.json({
      result: {
        data: newContract,
      },
    })
  }),

  // Update contract
  http.patch('/api/trpc/contracts.update', async ({ request }) => {
    const body = await request.json() as any
    const { id, ...updateData } = body

    const contractIndex = mockContracts.findIndex(c => c.id === id)

    if (contractIndex === -1) {
      return HttpResponse.json(
        {
          error: {
            message: 'Contract not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    const updatedContract = {
      ...mockContracts[contractIndex],
      ...updateData,
      updatedAt: new Date().toISOString(),
    }

    mockContracts[contractIndex] = updatedContract

    return HttpResponse.json({
      result: {
        data: updatedContract,
      },
    })
  }),

  // Delete contract
  http.delete('/api/trpc/contracts.delete', ({ request }) => {
    const url = new URL(request.url)
    const input = JSON.parse(url.searchParams.get('input') || '{}')
    const { id } = input

    const contractIndex = mockContracts.findIndex(c => c.id === id)

    if (contractIndex === -1) {
      return HttpResponse.json(
        {
          error: {
            message: 'Contract not found',
            code: 'NOT_FOUND',
          },
        },
        { status: 404 }
      )
    }

    mockContracts.splice(contractIndex, 1)

    return HttpResponse.json({
      result: {
        data: { success: true },
      },
    })
  }),

  // Get contract statistics
  http.get('/api/trpc/contracts.getStatistics', () => {
    const stats = {
      total: mockContracts.length,
      byStatus: {
        DRAFT: mockContracts.filter(c => c.status === 'DRAFT').length,
        REVIEW: mockContracts.filter(c => c.status === 'REVIEW').length,
        APPROVED: mockContracts.filter(c => c.status === 'APPROVED').length,
        SIGNED: mockContracts.filter(c => c.status === 'SIGNED').length,
        ACTIVE: mockContracts.filter(c => c.status === 'ACTIVE').length,
        EXPIRED: mockContracts.filter(c => c.status === 'EXPIRED').length,
        TERMINATED: mockContracts.filter(c => c.status === 'TERMINATED').length,
        REJECTED: mockContracts.filter(c => c.status === 'REJECTED').length,
      },
      totalValue: mockContracts.reduce((sum, c) => sum + (c.value || 0), 0),
      recentActivity: mockContracts.slice(0, 5),
    }

    return HttpResponse.json({
      result: {
        data: stats,
      },
    })
  }),
]
